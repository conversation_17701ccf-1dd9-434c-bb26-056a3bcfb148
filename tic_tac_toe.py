VERSION = "1.0.0"

import os

class TicTacToe:
    def __init__(self):
        self.board = [' ' for _ in range(9)]
        self.current_player = 'X'
    
    def display_board(self):
        os.system('cls' if os.name == 'nt' else 'clear')
        print(f"Tic-Tac-Toe v{VERSION}")
        print("=" * 20)
        print(f" {self.board[0]} | {self.board[1]} | {self.board[2]} ")
        print("-----------")
        print(f" {self.board[3]} | {self.board[4]} | {self.board[5]} ")
        print("-----------")
        print(f" {self.board[6]} | {self.board[7]} | {self.board[8]} ")
        print("\nPositionen: 1-9")
        print(" 1 | 2 | 3 ")
        print("-----------")
        print(" 4 | 5 | 6 ")
        print("-----------")
        print(" 7 | 8 | 9 ")
    
    def make_move(self, position):
        if self.board[position - 1] == ' ':
            self.board[position - 1] = self.current_player
            print(f"Debug: Spieler {self.current_player} setzt auf Position {position}")
            return True
        else:
            print("Debug: Position bereits belegt")
            return False
    
    def check_winner(self):
        # Gewinnkombinationen
        win_patterns = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8],  # Reihen
            [0, 3, 6], [1, 4, 7], [2, 5, 8],  # Spalten
            [0, 4, 8], [2, 4, 6]              # Diagonalen
        ]
        
        for pattern in win_patterns:
            if (self.board[pattern[0]] == self.board[pattern[1]] == 
                self.board[pattern[2]] != ' '):
                print(f"Debug: Gewinner gefunden - {self.board[pattern[0]]}")
                return self.board[pattern[0]]
        
        if ' ' not in self.board:
            print("Debug: Unentschieden")
            return 'Tie'
        
        return None
    
    def switch_player(self):
        self.current_player = 'O' if self.current_player == 'X' else 'X'
        print(f"Debug: Spielerwechsel zu {self.current_player}")
    
    def play(self):
        print("Willkommen zu Tic-Tac-Toe!")
        print("Spieler X beginnt")
        
        while True:
            self.display_board()
            
            try:
                position = int(input(f"\nSpieler {self.current_player}, wähle Position (1-9): "))
                
                if position < 1 or position > 9:
                    print("Ungültige Position! Wähle 1-9")
                    input("Drücke Enter...")
                    continue
                
                if self.make_move(position):
                    winner = self.check_winner()
                    
                    if winner:
                        self.display_board()
                        if winner == 'Tie':
                            print("\nUnentschieden!")
                        else:
                            print(f"\nSpieler {winner} gewinnt!")
                        break
                    
                    self.switch_player()
                else:
                    print("Position bereits belegt!")
                    input("Drücke Enter...")
                    
            except ValueError:
                print("Bitte gib eine Zahl ein!")
                input("Drücke Enter...")

def main():
    while True:
        game = TicTacToe()
        game.play()
        
        play_again = input("\nNochmal spielen? (j/n): ").lower()
        if play_again != 'j':
            print("Danke fürs Spielen!")
            break

if __name__ == "__main__":
    main()
