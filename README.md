# Industrial Log Analyzer

Ein umfassendes Python-Programm zur Analyse von Log-Dateien industrieller Automatisierungssysteme, speziell entwickelt für TwinCAT/EtherCat-Umgebungen.

## 🚀 Features

### Basis-Version (`log_analyzer.py`)
- **📊 Dashboard** mit Systemüberblick und Metriken
- **📈 Timeline-Analyse** zur chronologischen Fehlerbetrachtung
- **🔗 Fehlerkorrelation** zur Erkennung von Zusammenhängen
- **💚 System-Health-Analyse** mit Bewertung der Systemgesundheit
- **📋 Detaillierte Log-Ansicht** mit erweiterten Filtern
- **📄 Report-Generierung** für umfassende Analyseberichte

### Erweiterte Version (`advanced_log_analyzer.py`)
- **🎯 Erweiterte Dashboard** mit Live-Metriken und Alarmen
- **🔍 Pattern-Erkennung** für wiederkehrende Fehlermustern
- **⚡ Echtzeit-Monitoring** für Live-Überwachung
- **📤 Export-Funktionen** (Excel, CSV, JSON, PDF, HTML)
- **📧 E-Mail-Berichte** für automatische Benachrichtigungen
- **🎨 Verbesserte GUI** mit Icons und modernem Design

## 📋 Voraussetzungen

### Python-Version
- Python 3.7 oder höher

### Erforderliche Pakete
```bash
pip install pandas matplotlib numpy tkinter
```

### Optionale Pakete (für erweiterte Funktionen)
```bash
pip install seaborn plotly openpyxl xlsxwriter
```

## 🛠️ Installation

### Automatische Installation
1. Führen Sie das Installationsskript aus:
```bash
python install_dependencies.py
```

### Manuelle Installation
1. Klonen oder laden Sie das Repository herunter
2. Installieren Sie die erforderlichen Pakete:
```bash
pip install pandas matplotlib numpy
```
3. Starten Sie das Programm:
```bash
python log_analyzer.py
```
oder für die erweiterte Version:
```bash
python advanced_log_analyzer.py
```

## 📚 Verwendung

### 1. Log-Verzeichnis auswählen
- Klicken Sie auf "Browse" und wählen Sie das Verzeichnis mit den Log-Dateien aus
- Unterstützte Dateiformate: `.log`

### 2. Logs laden
- Klicken Sie auf "Load Logs", um alle Log-Dateien zu analysieren
- Das Programm erkennt automatisch verschiedene Log-Typen:
  - Fatal Errors
  - Malfunctions
  - Warnings
  - Health
  - FileBackup
  - UserActivity
  - Tracking
  - Grabber
  - Errors

### 3. Zeitraum festlegen
- Geben Sie Start- und Enddatum für die Analyse ein (Format: YYYY-MM-DD)
- Nutzen Sie die Schnell-Buttons für häufige Zeiträume

### 4. Analysen durchführen

#### Timeline-Analyse
- Visualisiert Fehler chronologisch
- Zeigt Fehlerverteilung über Zeit
- Erkennt Häufungen und Muster

#### Fehlerkorrelation
- Findet zeitliche Zusammenhänge zwischen Fehlern
- Analysiert kritische Fehlersequenzen
- Zeigt Systemkomponenten-Interaktionen

#### System-Health-Analyse
- Bewertet Gesamtsystemgesundheit (0-100%)
- Identifiziert problematische Komponenten
- Gibt Empfehlungen für Wartungsmaßnahmen

#### Pattern-Erkennung (nur erweiterte Version)
- Erkennt wiederkehrende Fehlermuster
- Zeitbasierte Muster
- Sequenzmuster
- Anomalie-Erkennung

### 5. Berichte exportieren
- Generieren Sie umfassende Analyseberichte
- Verschiedene Exportformate verfügbar
- Anpassbare Berichtsinhalte

## 🔧 Konfiguration

Die Datei `config.json` enthält konfigurierbare Einstellungen:

### Fehler-Pattern
Definiert Muster zur Kategorisierung von Fehlern:
```json
{
  "error_patterns": {
    "ethercat": {
      "pattern": "EtherCAT|EtherCat|Ecat|CVZ\\.|CBS\\.|CSW\\.",
      "description": "EtherCAT communication issues"
    }
  }
}
```

### Kritische Fehlercodes
Definiert bekannte kritische Fehlercodes:
```json
{
  "critical_codes": {
    "#1060": {
      "description": "Motorspannung nicht verfügbar",
      "severity": "critical"
    }
  }
}
```

### Analyse-Einstellungen
```json
{
  "analysis_settings": {
    "correlation_time_window_minutes": 5,
    "max_entries_detailed_view": 1000,
    "health_score_error_penalty": 2
  }
}
```

## 📊 Unterstützte Log-Formate

Das Programm erkennt automatisch Log-Einträge im Format:
```
YYYY-MM-DD HH:MM:SS.mmm|Nachricht
```

### Beispiele für unterstützte Logs:
- **EtherCat-Fehler**: `CVZ.5301-T1.1`, `CBS.2301-K1.1`
- **Kamera-Probleme**: `RC`, `RR`, `ImageProcessing`
- **Motor-Fehler**: `Motorspannung nicht verfügbar`
- **Sicherheitssystem**: `Schutztür offen`, `Sicherheitskreis`
- **Sensor-Probleme**: `verschmutzt`, `Lichtschranke`
- **Beleuchtung**: `Flash Unit 230V`

## 🎯 Anwendungsbeispiele

### Typische Problemanalyse
1. **Symptom**: RC/RR Kameras liefern dunkle Bilder
2. **Analyse**: Timeline-Analyse zeigt Türöffnung als Auslöser
3. **Korrelation**: Sicherheitssystem → Motorspannung → Beleuchtung
4. **Lösung**: Synchronisation der Kamera-Licht-Steuerung

### Präventive Wartung
1. **Health-Analyse** zeigt sich verschlechternde Komponenten
2. **Pattern-Erkennung** identifiziert wiederkehrende Probleme
3. **Empfehlungen** für vorbeugende Maßnahmen

## 🔍 Erweiterte Features

### Echtzeit-Monitoring
- Live-Überwachung eingehender Log-Einträge
- Sofortige Benachrichtigung bei kritischen Fehlern
- Trends und Entwicklungen in Echtzeit

### Intelligente Filter
- Mehrkriterien-Filterung
- Reguläre Ausdrücke
- Zeitraumbasierte Filter
- Severity-basierte Sortierung

### Export & Berichtswesen
- **Excel**: Strukturierte Datenexport mit Formatierung
- **CSV**: Für weitere Datenverarbeitung
- **JSON**: Für API-Integration
- **PDF**: Professionelle Berichte für Management
- **HTML**: Interaktive Web-Berichte

## 🐛 Fehlerbehebung

### Häufige Probleme

#### "tkinter not found"
```bash
# Windows
pip install tk

# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL
sudo yum install tkinter
```

#### "Encoding errors" beim Laden
- Das Programm verwendet automatisch UTF-8 mit Fehlerbehandlung
- Bei persistenten Problemen prüfen Sie die Log-Datei-Kodierung

#### Langsame Performance bei großen Logs
- Reduzieren Sie den Analysezeitraum
- Erhöhen Sie `max_entries_detailed_view` in der Konfiguration
- Nutzen Sie die Filter-Funktionen

## 📈 Roadmap

### Geplante Features
- [ ] **Machine Learning** für automatische Anomalie-Erkennung
- [ ] **Web-Interface** für Remote-Zugriff
- [ ] **Database-Integration** für historische Datenanalyse
- [ ] **Alerting-System** mit E-Mail/SMS-Benachrichtigungen
- [ ] **Multi-Site-Monitoring** für verteilte Anlagen
- [ ] **Predictive Maintenance** Algorithmen

## 📞 Support

Bei Fragen oder Problemen:
1. Prüfen Sie die Konsolen-Ausgabe auf Fehlermeldungen
2. Überprüfen Sie die Log-Datei-Formate
3. Testen Sie mit einer kleineren Datenmenge
4. Dokumentieren Sie Fehlermeldungen für Support-Anfragen

## 📄 Lizenz

Dieses Programm wurde für industrielle Anwendungen entwickelt und ist für den internen Gebrauch bestimmt.

---

**Version**: 2.0  
**Letzte Aktualisierung**: Juli 31, 2025  
**Kompatibilität**: TwinCAT, EtherCat, Symplex-Systeme
