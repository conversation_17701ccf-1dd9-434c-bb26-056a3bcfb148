#!/usr/bin/env python3
"""
Advanced Industrial Log Analyzer - Extended Features
====================================================
Enhanced version with advanced analytics, pattern recognition, and reporting capabilities.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import re
import os
import json
from datetime import datetime, timedelta
import threading
from collections import defaultdict, Counter
import statistics

class AdvancedLogAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Advanced Industrial Log Analyzer v2.0 - TwinCAT/EtherCat System")
        self.root.geometry("1600x1000")
        
        # Data storage
        self.log_data = {}
        self.analysis_results = {}
        self.log_directory = ""
        self.config = self.load_config()
        
        # Analysis cache
        self.cache = {}
        
        self.setup_gui()
        
    def load_config(self):
        """Load configuration from config.json"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default config if file not found
            return {
                "error_patterns": {
                    "ethercat": {"pattern": "EtherCAT|EtherCat|Ecat|CVZ\\.|CBS\\.|CSW\\."},
                    "camera": {"pattern": "RC|RR|camera|Camera|Kamera|ImageProcessing"},
                    "motor": {"pattern": "Motor|Servo|Antrieb|Achse|drive|enable"},
                    "safety": {"pattern": "Schutztür|Protection|Safety|#106[0-9]"},
                    "sensor": {"pattern": "sensor|Sensor|verschmutzt|dirty|Lichtschranke"},
                    "flash_unit": {"pattern": "Flash Unit|230V|Beleuchtung|lighting"},
                    "network": {"pattern": "192\\.168\\.|network|timeout|Connection"},
                    "communication": {"pattern": "Communication|NetMsg|Client|Server"}
                },
                "critical_codes": {
                    "#1060": {"description": "Motorspannung nicht verfügbar", "severity": "critical"},
                    "#1063": {"description": "Schutztür offen", "severity": "critical"},
                    "#1067": {"description": "Sicherheitskreis", "severity": "critical"},
                    "#1080": {"description": "Auslösesensor verschmutzt", "severity": "high"},
                    "#120": {"description": "Lichtschranke verschmutzt", "severity": "medium"}
                },
                "analysis_settings": {
                    "correlation_time_window_minutes": 5,
                    "max_entries_detailed_view": 1000,
                    "health_score_error_penalty": 2
                }
            }
    
    def setup_gui(self):
        """Setup enhanced GUI interface"""
        # Create main frame with better styling
        style = ttk.Style()
        style.theme_use('clam')
        
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # Enhanced file selection frame
        file_frame = ttk.LabelFrame(main_frame, text="📁 Log Directory & Settings", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Directory selection
        dir_frame = ttk.Frame(file_frame)
        dir_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.directory_var = tk.StringVar()
        ttk.Entry(dir_frame, textvariable=self.directory_var, width=100).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(dir_frame, text="Browse", command=self.select_directory).grid(row=0, column=1, padx=(5, 0))
        ttk.Button(dir_frame, text="🔄 Load Logs", command=self.load_logs).grid(row=0, column=2, padx=(10, 0))
        
        # Quick settings
        settings_frame = ttk.Frame(file_frame)
        settings_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(5, 0))
        
        ttk.Label(settings_frame, text="Auto-refresh:").grid(row=0, column=0, padx=(0, 5))
        self.auto_refresh_var = tk.BooleanVar()
        ttk.Checkbutton(settings_frame, variable=self.auto_refresh_var).grid(row=0, column=1, padx=(0, 15))
        
        ttk.Label(settings_frame, text="Max entries:").grid(row=0, column=2, padx=(0, 5))
        self.max_entries_var = tk.StringVar(value="10000")
        ttk.Entry(settings_frame, textvariable=self.max_entries_var, width=8).grid(row=0, column=3, padx=(0, 15))
        
        # Enhanced control panel
        control_frame = ttk.LabelFrame(main_frame, text="🔍 Analysis Controls", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Date range with calendar-like interface
        date_frame = ttk.Frame(control_frame)
        date_frame.grid(row=0, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(date_frame, text="📅 Start Date:").grid(row=0, column=0, padx=(0, 5))
        self.start_date = tk.StringVar(value="2025-07-30")
        ttk.Entry(date_frame, textvariable=self.start_date, width=12).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(date_frame, text="📅 End Date:").grid(row=0, column=2, padx=(0, 5))
        self.end_date = tk.StringVar(value="2025-07-31")
        ttk.Entry(date_frame, textvariable=self.end_date, width=12).grid(row=0, column=3, padx=(0, 20))
        
        # Quick date buttons
        quick_date_frame = ttk.Frame(date_frame)
        quick_date_frame.grid(row=0, column=4, padx=(10, 0))
        
        ttk.Button(quick_date_frame, text="Today", width=8, command=self.set_today).grid(row=0, column=0, padx=(0, 2))
        ttk.Button(quick_date_frame, text="Yesterday", width=10, command=self.set_yesterday).grid(row=0, column=1, padx=(2, 2))
        ttk.Button(quick_date_frame, text="Last 7 days", width=10, command=self.set_last_week).grid(row=0, column=2, padx=(2, 0))
        
        # Enhanced analysis buttons with icons
        analysis_frame = ttk.Frame(control_frame)
        analysis_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E))
        
        ttk.Button(analysis_frame, text="📊 Timeline Analysis", command=self.timeline_analysis).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(analysis_frame, text="🔗 Error Correlation", command=self.error_correlation).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(analysis_frame, text="💚 System Health", command=self.system_health_analysis).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(analysis_frame, text="🔍 Pattern Detection", command=self.pattern_analysis).grid(row=0, column=3, padx=(0, 5))
        ttk.Button(analysis_frame, text="⚡ Real-time Monitor", command=self.real_time_monitor).grid(row=0, column=4, padx=(0, 5))
        ttk.Button(analysis_frame, text="📋 Generate Report", command=self.generate_report).grid(row=0, column=5, padx=(0, 5))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Enhanced notebook with more tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create enhanced tabs
        self.create_dashboard_tab()
        self.create_timeline_tab()
        self.create_correlation_tab()
        self.create_health_tab()
        self.create_pattern_tab()
        self.create_realtime_tab()
        self.create_detailed_view_tab()
        self.create_export_tab()
        
        # Enhanced status bar with indicators
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=0, sticky=(tk.W))
        
        # System status indicators
        self.system_status = tk.StringVar(value="🔴 No Data")
        ttk.Label(status_frame, textvariable=self.system_status).grid(row=0, column=1, padx=(20, 0))
        
        self.error_count_status = tk.StringVar(value="Errors: 0")
        ttk.Label(status_frame, textvariable=self.error_count_status).grid(row=0, column=2, padx=(20, 0))
        
    def create_dashboard_tab(self):
        """Create comprehensive dashboard tab"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="📊 Dashboard")
        
        # Create main dashboard layout
        dashboard_paned = ttk.PanedWindow(dashboard_frame, orient=tk.HORIZONTAL)
        dashboard_paned.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Left panel - Key metrics and alerts
        left_panel = ttk.Frame(dashboard_paned)
        dashboard_paned.add(left_panel, weight=1)
        
        # Key metrics frame
        metrics_frame = ttk.LabelFrame(left_panel, text="🎯 Key Metrics", padding="10")
        metrics_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5), pady=(0, 5))
        
        self.metrics_text = scrolledtext.ScrolledText(metrics_frame, width=40, height=15)
        self.metrics_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Alerts frame
        alerts_frame = ttk.LabelFrame(left_panel, text="🚨 Active Alerts", padding="10")
        alerts_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        self.alerts_text = scrolledtext.ScrolledText(alerts_frame, width=40, height=10)
        self.alerts_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Right panel - Dashboard charts
        right_panel = ttk.Frame(dashboard_paned)
        dashboard_paned.add(right_panel, weight=2)
        
        # Dashboard charts
        self.dashboard_fig, self.dashboard_axes = plt.subplots(2, 2, figsize=(12, 10))
        self.dashboard_canvas = FigureCanvasTkAgg(self.dashboard_fig, right_panel)
        self.dashboard_canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        dashboard_frame.columnconfigure(0, weight=1)
        dashboard_frame.rowconfigure(0, weight=1)
        left_panel.columnconfigure(0, weight=1)
        left_panel.rowconfigure(0, weight=1)
        left_panel.rowconfigure(1, weight=1)
        right_panel.columnconfigure(0, weight=1)
        right_panel.rowconfigure(0, weight=1)
        metrics_frame.columnconfigure(0, weight=1)
        metrics_frame.rowconfigure(0, weight=1)
        alerts_frame.columnconfigure(0, weight=1)
        alerts_frame.rowconfigure(0, weight=1)
        
    def create_timeline_tab(self):
        """Enhanced timeline analysis tab"""
        timeline_frame = ttk.Frame(self.notebook)
        self.notebook.add(timeline_frame, text="📈 Timeline")
        
        # Timeline controls
        timeline_controls = ttk.Frame(timeline_frame)
        timeline_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Label(timeline_controls, text="Granularity:").grid(row=0, column=0, padx=(0, 5))
        self.timeline_granularity = tk.StringVar(value="1 hour")
        granularity_combo = ttk.Combobox(timeline_controls, textvariable=self.timeline_granularity, 
                                       values=["1 minute", "5 minutes", "15 minutes", "1 hour", "1 day"])
        granularity_combo.grid(row=0, column=1, padx=(0, 15))
        
        ttk.Label(timeline_controls, text="Category:").grid(row=0, column=2, padx=(0, 5))
        self.timeline_category = tk.StringVar(value="All")
        category_combo = ttk.Combobox(timeline_controls, textvariable=self.timeline_category,
                                    values=["All", "ethercat", "camera", "motor", "safety", "sensor"])
        category_combo.grid(row=0, column=3, padx=(0, 15))
        
        ttk.Button(timeline_controls, text="🔄 Refresh Timeline", command=self.timeline_analysis).grid(row=0, column=4)
        
        # Timeline chart
        self.timeline_fig, self.timeline_ax = plt.subplots(figsize=(14, 8))
        self.timeline_canvas = FigureCanvasTkAgg(self.timeline_fig, timeline_frame)
        self.timeline_canvas.get_tk_widget().grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        timeline_frame.columnconfigure(0, weight=1)
        timeline_frame.rowconfigure(1, weight=1)
        
    def create_correlation_tab(self):
        """Enhanced correlation analysis tab"""
        correlation_frame = ttk.Frame(self.notebook)
        self.notebook.add(correlation_frame, text="🔗 Correlations")
        
        # Correlation controls
        corr_controls = ttk.Frame(correlation_frame)
        corr_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Label(corr_controls, text="Time Window:").grid(row=0, column=0, padx=(0, 5))
        self.correlation_window = tk.StringVar(value="5 minutes")
        window_combo = ttk.Combobox(corr_controls, textvariable=self.correlation_window,
                                  values=["1 minute", "5 minutes", "15 minutes", "1 hour"])
        window_combo.grid(row=0, column=1, padx=(0, 15))
        
        ttk.Button(corr_controls, text="🔍 Analyze Correlations", command=self.error_correlation).grid(row=0, column=2)
        
        # Split into text and visualization
        corr_paned = ttk.PanedWindow(correlation_frame, orient=tk.HORIZONTAL)
        corr_paned.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Text analysis
        text_frame = ttk.Frame(corr_paned)
        corr_paned.add(text_frame, weight=1)
        
        self.correlation_text = scrolledtext.ScrolledText(text_frame, width=60, height=30)
        self.correlation_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Correlation matrix visualization
        viz_frame = ttk.Frame(corr_paned)
        corr_paned.add(viz_frame, weight=1)
        
        self.correlation_fig, self.correlation_ax = plt.subplots(figsize=(8, 8))
        self.correlation_canvas = FigureCanvasTkAgg(self.correlation_fig, viz_frame)
        self.correlation_canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        correlation_frame.columnconfigure(0, weight=1)
        correlation_frame.rowconfigure(1, weight=1)
        text_frame.columnconfigure(0, weight=1)
        text_frame.rowconfigure(0, weight=1)
        viz_frame.columnconfigure(0, weight=1)
        viz_frame.rowconfigure(0, weight=1)
        
    def create_health_tab(self):
        """Enhanced system health tab"""
        health_frame = ttk.Frame(self.notebook)
        self.notebook.add(health_frame, text="💚 Health")
        
        # Health overview with gauges
        health_paned = ttk.PanedWindow(health_frame, orient=tk.VERTICAL)
        health_paned.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Top panel - Health metrics and gauges
        health_top = ttk.Frame(health_paned)
        health_paned.add(health_top, weight=1)
        
        self.health_fig, self.health_axes = plt.subplots(1, 3, figsize=(15, 5))
        self.health_canvas = FigureCanvasTkAgg(self.health_fig, health_top)
        self.health_canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Bottom panel - Detailed health report
        health_bottom = ttk.Frame(health_paned)
        health_paned.add(health_bottom, weight=1)
        
        self.health_text = scrolledtext.ScrolledText(health_bottom, width=100, height=20)
        self.health_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        health_frame.columnconfigure(0, weight=1)
        health_frame.rowconfigure(0, weight=1)
        health_top.columnconfigure(0, weight=1)
        health_top.rowconfigure(0, weight=1)
        health_bottom.columnconfigure(0, weight=1)
        health_bottom.rowconfigure(0, weight=1)
        
    def create_pattern_tab(self):
        """Pattern detection and analysis tab"""
        pattern_frame = ttk.Frame(self.notebook)
        self.notebook.add(pattern_frame, text="🔍 Patterns")
        
        # Pattern controls
        pattern_controls = ttk.Frame(pattern_frame)
        pattern_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Label(pattern_controls, text="Pattern Type:").grid(row=0, column=0, padx=(0, 5))
        self.pattern_type = tk.StringVar(value="Recurring Errors")
        pattern_combo = ttk.Combobox(pattern_controls, textvariable=self.pattern_type,
                                   values=["Recurring Errors", "Time-based Patterns", "Sequence Patterns", "Anomaly Detection"])
        pattern_combo.grid(row=0, column=1, padx=(0, 15))
        
        ttk.Button(pattern_controls, text="🔍 Detect Patterns", command=self.pattern_analysis).grid(row=0, column=2)
        
        # Pattern results
        self.pattern_text = scrolledtext.ScrolledText(pattern_frame, width=120, height=35)
        self.pattern_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        pattern_frame.columnconfigure(0, weight=1)
        pattern_frame.rowconfigure(1, weight=1)
        
    def create_realtime_tab(self):
        """Real-time monitoring tab"""
        realtime_frame = ttk.Frame(self.notebook)
        self.notebook.add(realtime_frame, text="⚡ Real-time")
        
        # Real-time controls
        rt_controls = ttk.Frame(realtime_frame)
        rt_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.rt_enabled = tk.BooleanVar()
        ttk.Checkbutton(rt_controls, text="Enable Real-time Monitoring", variable=self.rt_enabled, 
                       command=self.toggle_realtime).grid(row=0, column=0, padx=(0, 15))
        
        ttk.Label(rt_controls, text="Refresh Rate:").grid(row=0, column=1, padx=(0, 5))
        self.rt_refresh_rate = tk.StringVar(value="5 seconds")
        rate_combo = ttk.Combobox(rt_controls, textvariable=self.rt_refresh_rate,
                                values=["1 second", "5 seconds", "10 seconds", "30 seconds"])
        rate_combo.grid(row=0, column=2, padx=(0, 15))
        
        # Real-time display
        rt_paned = ttk.PanedWindow(realtime_frame, orient=tk.HORIZONTAL)
        rt_paned.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Live log feed
        feed_frame = ttk.LabelFrame(rt_paned, text="📡 Live Log Feed", padding="5")
        rt_paned.add(feed_frame, weight=1)
        
        self.realtime_text = scrolledtext.ScrolledText(feed_frame, width=60, height=30)
        self.realtime_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Real-time charts
        charts_frame = ttk.LabelFrame(rt_paned, text="📊 Live Metrics", padding="5")
        rt_paned.add(charts_frame, weight=1)
        
        self.realtime_fig, self.realtime_ax = plt.subplots(2, 1, figsize=(8, 10))
        self.realtime_canvas = FigureCanvasTkAgg(self.realtime_fig, charts_frame)
        self.realtime_canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        realtime_frame.columnconfigure(0, weight=1)
        realtime_frame.rowconfigure(1, weight=1)
        feed_frame.columnconfigure(0, weight=1)
        feed_frame.rowconfigure(0, weight=1)
        charts_frame.columnconfigure(0, weight=1)
        charts_frame.rowconfigure(0, weight=1)
        
    def create_detailed_view_tab(self):
        """Enhanced detailed log view tab"""
        detailed_frame = ttk.Frame(self.notebook)
        self.notebook.add(detailed_frame, text="📋 Log Details")
        
        # Enhanced filter frame
        filter_frame = ttk.LabelFrame(detailed_frame, text="🔍 Advanced Filters", padding="10")
        filter_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # First row of filters
        filter_row1 = ttk.Frame(filter_frame)
        filter_row1.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Label(filter_row1, text="Log Type:").grid(row=0, column=0, padx=(0, 5))
        self.log_type_var = tk.StringVar()
        log_type_combo = ttk.Combobox(filter_row1, textvariable=self.log_type_var, width=20)
        log_type_combo.grid(row=0, column=1, padx=(0, 15))
        
        ttk.Label(filter_row1, text="Severity:").grid(row=0, column=2, padx=(0, 5))
        self.severity_filter = tk.StringVar(value="All")
        severity_combo = ttk.Combobox(filter_row1, textvariable=self.severity_filter,
                                    values=["All", "critical", "high", "medium", "low"])
        severity_combo.grid(row=0, column=3, padx=(0, 15))
        
        ttk.Label(filter_row1, text="Category:").grid(row=0, column=4, padx=(0, 5))
        self.category_filter = tk.StringVar(value="All")
        category_combo = ttk.Combobox(filter_row1, textvariable=self.category_filter,
                                    values=["All", "ethercat", "camera", "motor", "safety", "sensor"])
        category_combo.grid(row=0, column=5, padx=(0, 15))
        
        # Second row of filters
        filter_row2 = ttk.Frame(filter_frame)
        filter_row2.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        ttk.Label(filter_row2, text="Search:").grid(row=0, column=0, padx=(0, 5))
        self.search_var = tk.StringVar()
        ttk.Entry(filter_row2, textvariable=self.search_var, width=40).grid(row=0, column=1, padx=(0, 15))
        
        ttk.Button(filter_row2, text="🔍 Apply Filters", command=self.apply_filter).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(filter_row2, text="🗑️ Clear Filters", command=self.clear_filters).grid(row=0, column=3, padx=(0, 5))
        ttk.Button(filter_row2, text="💾 Export Filtered", command=self.export_filtered).grid(row=0, column=4)
        
        # Detailed log view with line numbers
        log_view_frame = ttk.Frame(detailed_frame)
        log_view_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.detailed_text = scrolledtext.ScrolledText(log_view_frame, width=150, height=30, font=("Consolas", 9))
        self.detailed_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure weights
        detailed_frame.columnconfigure(0, weight=1)
        detailed_frame.rowconfigure(1, weight=1)
        filter_frame.columnconfigure(0, weight=1)
        log_view_frame.columnconfigure(0, weight=1)
        log_view_frame.rowconfigure(0, weight=1)
        
    def create_export_tab(self):
        """Export and reporting tab"""
        export_frame = ttk.Frame(self.notebook)
        self.notebook.add(export_frame, text="📤 Export")
        
        # Export options
        export_options_frame = ttk.LabelFrame(export_frame, text="📁 Export Options", padding="10")
        export_options_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Export format selection
        format_frame = ttk.Frame(export_options_frame)
        format_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(format_frame, text="Export Format:").grid(row=0, column=0, padx=(0, 5))
        self.export_format = tk.StringVar(value="Excel (.xlsx)")
        format_combo = ttk.Combobox(format_frame, textvariable=self.export_format,
                                  values=["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)", "PDF Report (.pdf)", "HTML Report (.html)"])
        format_combo.grid(row=0, column=1, padx=(0, 15))
        
        # Export content selection
        content_frame = ttk.LabelFrame(export_options_frame, text="Content Selection", padding="5")
        content_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.export_raw_logs = tk.BooleanVar(value=True)
        self.export_analysis = tk.BooleanVar(value=True)
        self.export_charts = tk.BooleanVar(value=True)
        self.export_summary = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(content_frame, text="Raw Log Data", variable=self.export_raw_logs).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(content_frame, text="Analysis Results", variable=self.export_analysis).grid(row=0, column=1, sticky=tk.W)
        ttk.Checkbutton(content_frame, text="Charts & Graphs", variable=self.export_charts).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(content_frame, text="Executive Summary", variable=self.export_summary).grid(row=1, column=1, sticky=tk.W)
        
        # Export buttons
        export_buttons_frame = ttk.Frame(export_options_frame)
        export_buttons_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(export_buttons_frame, text="📊 Quick Export", command=self.quick_export).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(export_buttons_frame, text="📋 Custom Export", command=self.custom_export).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(export_buttons_frame, text="📧 Email Report", command=self.email_report).grid(row=0, column=2, padx=(0, 5))
        
        # Export preview
        preview_frame = ttk.LabelFrame(export_frame, text="📄 Export Preview", padding="10")
        preview_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.export_preview = scrolledtext.ScrolledText(preview_frame, width=120, height=25)
        self.export_preview.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        export_frame.columnconfigure(0, weight=1)
        export_frame.rowconfigure(1, weight=1)
        export_options_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
    
    # Quick date setting methods
    def set_today(self):
        today = datetime.now().strftime('%Y-%m-%d')
        self.start_date.set(today)
        self.end_date.set(today)
        
    def set_yesterday(self):
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        self.start_date.set(yesterday)
        self.end_date.set(yesterday)
        
    def set_last_week(self):
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        self.start_date.set(start_date)
        self.end_date.set(end_date)
    
    # Enhanced analysis methods
    def pattern_analysis(self):
        """Advanced pattern detection and analysis"""
        if not self.log_data:
            messagebox.showwarning("Warning", "Please load log files first")
            return
            
        self.status_var.set("Detecting patterns...")
        pattern_type = self.pattern_type.get()
        
        report = f"PATTERN ANALYSIS - {pattern_type}\n"
        report += "=" * 80 + "\n\n"
        
        # Implement different pattern detection algorithms based on type
        if pattern_type == "Recurring Errors":
            report += self.detect_recurring_patterns()
        elif pattern_type == "Time-based Patterns":
            report += self.detect_time_patterns()
        elif pattern_type == "Sequence Patterns":
            report += self.detect_sequence_patterns()
        elif pattern_type == "Anomaly Detection":
            report += self.detect_anomalies()
        
        self.pattern_text.delete(1.0, tk.END)
        self.pattern_text.insert(tk.END, report)
        
        self.status_var.set("Pattern analysis complete")
        self.notebook.select(4)  # Switch to pattern tab
    
    def detect_recurring_patterns(self):
        """Detect recurring error patterns"""
        # Implementation for recurring pattern detection
        return "Recurring pattern analysis not yet implemented in this demo version.\n"
    
    def detect_time_patterns(self):
        """Detect time-based patterns"""
        # Implementation for time-based pattern detection
        return "Time-based pattern analysis not yet implemented in this demo version.\n"
    
    def detect_sequence_patterns(self):
        """Detect error sequence patterns"""
        # Implementation for sequence pattern detection
        return "Sequence pattern analysis not yet implemented in this demo version.\n"
    
    def detect_anomalies(self):
        """Detect anomalous behavior"""
        # Implementation for anomaly detection
        return "Anomaly detection not yet implemented in this demo version.\n"
    
    def real_time_monitor(self):
        """Start real-time monitoring"""
        self.rt_enabled.set(True)
        self.toggle_realtime()
        self.notebook.select(5)  # Switch to real-time tab
    
    def toggle_realtime(self):
        """Toggle real-time monitoring on/off"""
        if self.rt_enabled.get():
            self.status_var.set("Real-time monitoring enabled")
            # Start real-time monitoring thread
            # This is a placeholder for actual implementation
        else:
            self.status_var.set("Real-time monitoring disabled")
    
    def clear_filters(self):
        """Clear all filters"""
        self.log_type_var.set("")
        self.severity_filter.set("All")
        self.category_filter.set("All")
        self.search_var.set("")
    
    def export_filtered(self):
        """Export currently filtered data"""
        messagebox.showinfo("Export", "Export filtered data functionality to be implemented")
    
    def quick_export(self):
        """Quick export with default settings"""
        messagebox.showinfo("Export", "Quick export functionality to be implemented")
    
    def custom_export(self):
        """Custom export with user settings"""
        messagebox.showinfo("Export", "Custom export functionality to be implemented")
    
    def email_report(self):
        """Email report functionality"""
        messagebox.showinfo("Email", "Email report functionality to be implemented")
    
    # Inherit other methods from the base class
    def select_directory(self):
        """Select log directory"""
        directory = filedialog.askdirectory(title="Select Log Directory")
        if directory:
            self.directory_var.set(directory)
            self.log_directory = directory
    
    def load_logs(self):
        """Load all log files from the selected directory"""
        if not self.log_directory:
            messagebox.showerror("Error", "Please select a log directory first")
            return
            
        self.status_var.set("Loading log files...")
        self.progress_var.set(0)
        
        # Run in separate thread
        thread = threading.Thread(target=self._load_logs_thread)
        thread.daemon = True
        thread.start()
    
    def _load_logs_thread(self):
        """Load logs in separate thread with progress updates"""
        try:
            self.log_data = {}
            
            # Find all log files
            log_files = []
            for root, dirs, files in os.walk(self.log_directory):
                for file in files:
                    if file.endswith('.log'):
                        log_files.append(os.path.join(root, file))
            
            total_files = len(log_files)
            for i, log_file in enumerate(log_files):
                # Update progress
                progress = (i / total_files) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
                self.root.after(0, lambda i=i, t=total_files, f=log_file: 
                              self.status_var.set(f"Loading {i+1}/{t}: {os.path.basename(f)}"))
                
                # Determine log type and parse
                log_type = self._determine_log_type(log_file)
                if log_type not in self.log_data:
                    self.log_data[log_type] = []
                
                entries = self._parse_log_file(log_file)
                self.log_data[log_type].extend(entries)
            
            # Complete loading
            self.root.after(0, self._update_gui_after_load)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Error loading logs: {str(e)}"))
            self.root.after(0, lambda: self.status_var.set("Error loading logs"))
    
    def _determine_log_type(self, filename):
        """Determine log type from filename"""
        basename = os.path.basename(filename).lower()
        
        if 'fatal' in basename:
            return 'Fatal Errors'
        elif 'malfunction' in basename:
            return 'Malfunctions'
        elif 'warning' in basename:
            return 'Warnings'
        elif 'health' in basename:
            return 'Health'
        elif 'filebackup' in basename:
            return 'FileBackup'
        elif 'useractivity' in basename:
            return 'UserActivity'
        elif 'tracking' in basename:
            return 'Tracking'
        elif 'grabber' in basename:
            return 'Grabber'
        elif 'error' in basename:
            return 'Errors'
        else:
            return 'Other'
    
    def _parse_log_file(self, filename):
        """Parse individual log file"""
        entries = []
        
        try:
            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    # Extract timestamp and message
                    timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+)', line)
                    if timestamp_match:
                        timestamp_str = timestamp_match.group(1)
                        try:
                            timestamp = datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')
                            message = line[len(timestamp_str):].strip('|').strip()
                            
                            entry = {
                                'timestamp': timestamp,
                                'message': message,
                                'file': os.path.basename(filename),
                                'line': line_num,
                                'raw_line': line
                            }
                            
                            # Categorize error
                            entry['category'] = self._categorize_error(message)
                            entry['severity'] = self._determine_severity(message)
                            
                            entries.append(entry)
                        except ValueError:
                            continue
        except Exception as e:
            print(f"Error parsing {filename}: {e}")
        
        return entries
    
    def _categorize_error(self, message):
        """Categorize error based on message content"""
        message_lower = message.lower()
        
        for category, pattern_info in self.config['error_patterns'].items():
            pattern = pattern_info['pattern'] if isinstance(pattern_info, dict) else pattern_info
            if re.search(pattern, message, re.IGNORECASE):
                return category
        
        return 'other'
    
    def _determine_severity(self, message):
        """Determine error severity"""
        message_lower = message.lower()
        
        # Check for critical error codes
        for code, info in self.config['critical_codes'].items():
            if code in message:
                return info['severity']
        
        if any(word in message_lower for word in ['fatal', 'error', 'fehler', 'fail']):
            return 'high'
        elif any(word in message_lower for word in ['warning', 'warnung', 'caution']):
            return 'medium'
        else:
            return 'low'
    
    def _update_gui_after_load(self):
        """Update GUI after logs are loaded"""
        total_entries = sum(len(entries) for entries in self.log_data.values())
        self.status_var.set(f"Loaded {total_entries} log entries from {len(self.log_data)} log types")
        self.progress_var.set(100)
        
        # Update system status
        if total_entries > 0:
            error_count = sum(1 for entries in self.log_data.values() 
                            for entry in entries if entry['severity'] in ['critical', 'high'])
            
            if error_count > 100:
                self.system_status.set("🔴 Critical Issues Detected")
            elif error_count > 50:
                self.system_status.set("🟡 System Warnings")
            else:
                self.system_status.set("🟢 System Normal")
            
            self.error_count_status.set(f"Errors: {error_count}")
        
        # Update combo boxes with available log types
        log_types = list(self.log_data.keys())
        # Update comboboxes (simplified for demo)
        
        # Generate initial dashboard
        self.update_dashboard()
    
    def update_dashboard(self):
        """Update main dashboard with key metrics"""
        if not self.log_data:
            return
        
        # Calculate key metrics
        total_entries = sum(len(entries) for entries in self.log_data.values())
        category_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        recent_alerts = []
        
        # Analyze data
        for log_type, entries in self.log_data.items():
            for entry in entries:
                category_counts[entry['category']] += 1
                severity_counts[entry['severity']] += 1
                
                # Collect recent critical alerts
                if entry['severity'] == 'critical':
                    recent_alerts.append(entry)
        
        # Sort alerts by timestamp
        recent_alerts.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Update metrics text
        metrics_content = f"SYSTEM METRICS\n{'='*30}\n\n"
        metrics_content += f"📊 Total Entries: {total_entries:,}\n"
        metrics_content += f"🔴 Critical: {severity_counts.get('critical', 0)}\n"
        metrics_content += f"🟠 High: {severity_counts.get('high', 0)}\n"
        metrics_content += f"🟡 Medium: {severity_counts.get('medium', 0)}\n"
        metrics_content += f"🟢 Low: {severity_counts.get('low', 0)}\n\n"
        
        metrics_content += "TOP CATEGORIES:\n" + "-"*20 + "\n"
        for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
            metrics_content += f"{category}: {count}\n"
        
        self.metrics_text.delete(1.0, tk.END)
        self.metrics_text.insert(tk.END, metrics_content)
        
        # Update alerts
        alerts_content = "RECENT CRITICAL ALERTS\n" + "="*30 + "\n\n"
        for alert in recent_alerts[:10]:
            alerts_content += f"🚨 {alert['timestamp']}\n"
            alerts_content += f"   {alert['message'][:60]}...\n\n"
        
        self.alerts_text.delete(1.0, tk.END)
        self.alerts_text.insert(tk.END, alerts_content)
        
        # Update dashboard charts
        self.update_dashboard_charts(category_counts, severity_counts)
    
    def update_dashboard_charts(self, category_counts, severity_counts):
        """Update dashboard charts"""
        # Clear all axes
        for ax in self.dashboard_axes.flat:
            ax.clear()
        
        # Category distribution pie chart
        if category_counts:
            categories = list(category_counts.keys())[:6]  # Top 6 categories
            counts = [category_counts[cat] for cat in categories]
            self.dashboard_axes[0, 0].pie(counts, labels=categories, autopct='%1.1f%%')
            self.dashboard_axes[0, 0].set_title('Error Categories')
        
        # Severity bar chart
        if severity_counts:
            severities = list(severity_counts.keys())
            counts = list(severity_counts.values())
            colors = {'critical': 'red', 'high': 'orange', 'medium': 'yellow', 'low': 'green'}
            bar_colors = [colors.get(s, 'blue') for s in severities]
            self.dashboard_axes[0, 1].bar(severities, counts, color=bar_colors)
            self.dashboard_axes[0, 1].set_title('Severity Distribution')
        
        # Timeline of errors (last 24 hours)
        self.dashboard_axes[1, 0].set_title('Error Timeline (24h)')
        self.dashboard_axes[1, 0].text(0.5, 0.5, 'Timeline chart\n(to be implemented)', 
                                     ha='center', va='center', transform=self.dashboard_axes[1, 0].transAxes)
        
        # System health gauge
        self.dashboard_axes[1, 1].set_title('System Health')
        # Simple health score calculation
        total_errors = sum(severity_counts.values())
        health_score = max(0, 100 - (total_errors / 100))
        
        # Simple gauge representation
        self.dashboard_axes[1, 1].pie([health_score, 100-health_score], 
                                    colors=['green' if health_score > 70 else 'orange' if health_score > 40 else 'red', 'lightgray'],
                                    startangle=90, counterclock=False)
        self.dashboard_axes[1, 1].text(0, 0, f'{health_score:.0f}%', ha='center', va='center', fontsize=16, fontweight='bold')
        
        self.dashboard_canvas.draw()
    
    # Placeholder methods for timeline, correlation, and health analysis
    def timeline_analysis(self):
        """Timeline analysis with enhanced features"""
        self.status_var.set("Timeline analysis not fully implemented in demo version")
        
    def error_correlation(self):
        """Enhanced error correlation analysis"""
        self.status_var.set("Error correlation analysis not fully implemented in demo version")
        
    def system_health_analysis(self):
        """Enhanced system health analysis"""
        self.status_var.set("System health analysis not fully implemented in demo version")
    
    def apply_filter(self):
        """Apply enhanced filters to detailed log view"""
        self.status_var.set("Enhanced filtering not fully implemented in demo version")
    
    def generate_report(self):
        """Generate comprehensive report"""
        self.status_var.set("Report generation not fully implemented in demo version")

def main():
    """Main application entry point"""
    root = tk.Tk()
    app = AdvancedLogAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
