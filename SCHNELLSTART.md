# 🚀 Schnellstart-Anleitung

## Industrial Log Analyzer für Ihre Symplex-Anlage

### ⚡ Sofort starten
1. **<PERSON><PERSON>klick** auf `start_analyzer.bat`
2. Wählen Sie Version 1 (Basic) für den ersten Test
3. Das Programm öffnet sich automatisch

### 📁 Erste Analyse
1. **Browse** → Ihr Log-Ordner auswählen (`c:\Users\<USER>\Downloads\logs`)
2. **Load Logs** → Alle Dateien werden automatisch geladen
3. **Timeline Analysis** → Zeigt chronologische Fehlerverteilung

### 🎯 Für Ihr spezifisches Problem (RC/RR Kameras)
1. **Zeitraum einstellen**: 2025-07-30 bis 2025-07-31
2. **Error Correlation** klicken
3. Im Bericht nach **Türöffnung → Motorspannung → Kamera-Synchronisation** suchen

### 📊 Vorgenerierte Analyse
Das Programm erkennt automatisch:
- ✅ **EtherCat-Probleme** (CVZ.5301, CBS.2301)
- ✅ **Kamera-Kommunikation** (RC, RR, ImageProcessing)
- ✅ **Motorspannung** (#1060)
- ✅ **Schutztür** (#1063, #1067)
- ✅ **Sensor-Verschmutzung** (#1080, #120)
- ✅ **Flash Unit 230V** Beleuchtungsprobleme

### 🔧 Erweiterte Funktionen
- **System Health**: Gesamtbewertung der Anlagengesundheit
- **Pattern Detection**: Wiederkehrende Fehlermustern
- **Export**: Berichte als Excel/PDF für Management

### 🆘 Bei Problemen
1. Programm schließen und `start_analyzer.bat` neu ausführen
2. Bei Fehlern: README.md → Abschnitt "Fehlerbehebung"
3. Log-Dateien prüfen: Sollten das Format `YYYY-MM-DD HH:MM:SS.mmm|Nachricht` haben

### 📈 Ihr konkreter Fall
**Problem**: RC/RR Kameras dunkle Bilder seit gestern morgen
**Lösung im Programm**:
1. Timeline-Analyse zeigt Türöffnung um 06:42:17
2. Korrelations-Analyse zeigt: Türöffnung → Sicherheitssystem → Motorspannung aus → Beleuchtung unsynchron
3. Health-Analyse bewertet betroffene Komponenten

**Empfehlung**: 
- Kamera-Licht-Synchronisation nach Sicherheitsereignissen überprüfen
- Automatische Rekalibrierung nach Türschließung implementieren

---
*Version 2.0 | Optimiert für TwinCAT/EtherCat/Symplex-Systeme*
