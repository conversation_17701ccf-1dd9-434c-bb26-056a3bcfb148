2025-07-31 06:40:23.6225|Process 5856/ParameterUI (M-Base)|Thread 114/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 06:40:23.7425|Process 5856/ParameterUI (M-Base)|Thread 67/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 06:40:23.9225|Process 5856/ParameterUI (M-Base)|Thread 94/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 06:40:24.7032|Process 5856/ParameterUI (M-Base)|Thread 49/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 06:40:29.1024|Process 5856/ParameterUI (M-Base)|Thread 99/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 06:40:30.7254|Process 5856/ParameterUI (M-Base)|Thread 3/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 06:40:32.1044|Process 5856/ParameterUI (M-Base)|Thread 61/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 06:40:33.4511|Process 5856/ParameterUI (M-Base)|Thread 3/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 07:30:33.1203|Process 7804/Main (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 07:30:33.1563|Process 7804/Main (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 07:30:42.0249|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: ReadContents(System.Collections.Generic.Dictionary`2[System.String,System.Collections.Generic.Dictionary`2[System.String,System.Object]], -7).PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 07:30:42.0249|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 07:30:42.0249|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 11, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 07:30:42.0249|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -16, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 272
2025-07-31 07:30:42.0279|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -5, Stack: EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 07:30:42.0279|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 6001, Stack:  The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.UIClasses.ParameterUIRemoteControl.EditContainerMessage.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\UIClasses\BinaryMessageSerialization.cs:line 79
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 07:31:23.6226|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: ReadContents(System.Collections.Generic.Dictionary`2[System.String,System.Collections.Generic.Dictionary`2[System.String,System.Object]], -7).PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 07:31:23.6226|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 07:31:23.6226|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 11, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 07:31:23.6226|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -16, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 272
2025-07-31 07:31:23.6226|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -5, Stack: EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 07:31:23.6226|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 6001, Stack:  The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.UIClasses.ParameterUIRemoteControl.EditContainerMessage.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\UIClasses\BinaryMessageSerialization.cs:line 79
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 07:40:38.8117|Process 5856/ParameterUI (M-Base)|Thread 96/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 07:40:40.1039|Process 5856/ParameterUI (M-Base)|Thread 73/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:19:57.8137|Process 7804/Main (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 08:19:57.8593|Process 7804/Main (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 08:20:06.8965|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: ReadContents(System.Collections.Generic.Dictionary`2[System.String,System.Collections.Generic.Dictionary`2[System.String,System.Object]], -7).PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:20:06.8965|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:20:06.8965|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 11, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:20:06.8965|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -16, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 272
2025-07-31 08:20:06.8965|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -5, Stack: EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:20:06.8965|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 6001, Stack:  The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.UIClasses.ParameterUIRemoteControl.EditContainerMessage.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\UIClasses\BinaryMessageSerialization.cs:line 79
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:20:32.1967|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: ReadContents(System.Collections.Generic.Dictionary`2[System.String,System.Collections.Generic.Dictionary`2[System.String,System.Object]], -7).PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:20:32.1967|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:20:32.1967|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 11, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:20:32.1967|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -16, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 272
2025-07-31 08:20:32.1967|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -5, Stack: EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:20:32.1967|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 6001, Stack:  The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.UIClasses.ParameterUIRemoteControl.EditContainerMessage.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\UIClasses\BinaryMessageSerialization.cs:line 79
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:40:46.0784|Process 5856/ParameterUI (M-Base)|Thread 91/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:43:43.9423|Process 7804/Main (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 08:43:43.9773|Process 7804/Main (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 08:44:31.9813|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: ReadContents(System.Collections.Generic.Dictionary`2[System.String,System.Collections.Generic.Dictionary`2[System.String,System.Object]], -7).PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:44:31.9813|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:44:31.9813|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 11, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:44:31.9813|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -16, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 272
2025-07-31 08:44:31.9813|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -5, Stack: EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:44:31.9813|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 6001, Stack:  The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.UIClasses.ParameterUIRemoteControl.EditContainerMessage.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\UIClasses\BinaryMessageSerialization.cs:line 79
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:45:33.6572|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: ReadContents(System.Collections.Generic.Dictionary`2[System.String,System.Collections.Generic.Dictionary`2[System.String,System.Object]], -7).PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:45:33.6572|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -7, Stack: PictureSet.ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:45:33.6572|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 11, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet], -16).ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:45:33.6572|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -16, Stack: ReadContents(System.Collections.Generic.KeyValuePair`2[System.String,Symplex.BaseInterfaces.PictureSet][], -5).EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 272
2025-07-31 08:45:33.6572|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id -5, Stack: EditContainerMessage The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 270
2025-07-31 08:45:33.6572|Process 5856/ParameterUI (M-Base)|Thread 24/Connect/Receive thread AutoReconnectSocketClient(Main_Parameter_Connection, 127.0.0.1:4550, Connected=False)|FATAL|BinaryMessageReader|Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject|Error reading message with id 6001, Stack:  The value "System.Double[]" is not of type "System.String" and cannot be used in this generic collection.
Parameter name: key, System.ArgumentException, Void ThrowWrongKeyTypeArgumentException(System.Object, System.Type),    at System.ThrowHelper.ThrowWrongKeyTypeArgumentException(Object key, Type targetType)
   at System.Collections.Generic.Dictionary`2.System.Collections.IDictionary.Add(Object key, Object value)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 441
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 446
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.BaseInterfaces.PictureSet.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\BaseInterfaces\BinaryMessageSerialization.cs:line 604
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 455
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.<ReadObjectList>d__50.MoveNext() in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 473
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadContents(Type t, Int16 msgId) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 417
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 304
   at Symplex.UIClasses.ParameterUIRemoteControl.EditContainerMessage.ReadBinary(IBinaryStreamReader srcStream) in E:\git\chili_main\common\UIClasses\BinaryMessageSerialization.cs:line 79
   at Symplex.Communication.BinaryMessages.BinaryMessageReader.ReadObject(Object buffer) in E:\git\chili_main\common\Communication\BinaryMessages\BinaryMessageReader.cs:line 294
2025-07-31 08:46:06.6831|Process 5856/ParameterUI (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 08:47:23.9211|Process 5856/ParameterUI (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 08:48:33.4817|Process 5856/ParameterUI (M-Base)|Thread 56/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:33.8497|Process 5856/ParameterUI (M-Base)|Thread 115/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:33.9237|Process 5856/ParameterUI (M-Base)|Thread 9/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:34.2127|Process 5856/ParameterUI (M-Base)|Thread 34/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:35.1127|Process 5856/ParameterUI (M-Base)|Thread 73/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:36.3977|Process 5856/ParameterUI (M-Base)|Thread 30/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:37.6357|Process 5856/ParameterUI (M-Base)|Thread 94/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:38.8731|Process 5856/ParameterUI (M-Base)|Thread 33/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:40.0819|Process 5856/ParameterUI (M-Base)|Thread 46/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:48:59.1564|Process 5856/ParameterUI (M-Base)|Thread 51/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:49:00.2944|Process 5856/ParameterUI (M-Base)|Thread 5/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:49:10.7503|Process 5856/ParameterUI (M-Base)|Thread 123/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:49:11.8843|Process 5856/ParameterUI (M-Base)|Thread 87/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:49:18.7193|Process 5856/ParameterUI (M-Base)|Thread 97/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:49:19.9823|Process 5856/ParameterUI (M-Base)|Thread 18/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:50:44.8680|Process 5856/ParameterUI (M-Base)|Thread 18/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:50:46.1669|Process 5856/ParameterUI (M-Base)|Thread 97/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:50:59.3882|Process 5856/ParameterUI (M-Base)|Thread 84/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:50:59.5972|Process 5856/ParameterUI (M-Base)|Thread 9/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:50:59.7222|Process 5856/ParameterUI (M-Base)|Thread 115/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:00.0082|Process 5856/ParameterUI (M-Base)|Thread 46/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:00.1202|Process 5856/ParameterUI (M-Base)|Thread 86/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:00.1482|Process 5856/ParameterUI (M-Base)|Thread 92/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:03.0972|Process 5856/ParameterUI (M-Base)|Thread 9/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:03.2642|Process 5856/ParameterUI (M-Base)|Thread 84/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:03.4382|Process 5856/ParameterUI (M-Base)|Thread 86/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:03.5452|Process 5856/ParameterUI (M-Base)|Thread 92/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:03.7679|Process 5856/ParameterUI (M-Base)|Thread 46/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:03.7781|Process 5856/ParameterUI (M-Base)|Thread 115/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:09.3255|Process 5856/ParameterUI (M-Base)|Thread 5/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:09.4655|Process 5856/ParameterUI (M-Base)|Thread 61/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:09.9701|Process 5856/ParameterUI (M-Base)|Thread 27/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:10.0631|Process 5856/ParameterUI (M-Base)|Thread 18/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:10.1709|Process 5856/ParameterUI (M-Base)|Thread 89/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:10.1709|Process 5856/ParameterUI (M-Base)|Thread 34/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:10.7589|Process 5856/ParameterUI (M-Base)|Thread 92/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:10.8019|Process 5856/ParameterUI (M-Base)|Thread 91/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:11.1999|Process 5856/ParameterUI (M-Base)|Thread 51/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:11.4229|Process 5856/ParameterUI (M-Base)|Thread 97/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:11.6339|Process 5856/ParameterUI (M-Base)|Thread 90/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:11.6849|Process 5856/ParameterUI (M-Base)|Thread 123/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:12.1165|Process 5856/ParameterUI (M-Base)|Thread 50/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:12.4642|Process 5856/ParameterUI (M-Base)|Thread 107/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:12.7250|Process 5856/ParameterUI (M-Base)|Thread 94/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:12.9570|Process 5856/ParameterUI (M-Base)|Thread 56/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:13.0990|Process 5856/ParameterUI (M-Base)|Thread 96/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:13.2810|Process 5856/ParameterUI (M-Base)|Thread 33/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:13.3480|Process 5856/ParameterUI (M-Base)|Thread 113/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:20.1682|Process 5856/ParameterUI (M-Base)|Thread 34/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:20.2842|Process 5856/ParameterUI (M-Base)|Thread 89/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:20.4092|Process 5856/ParameterUI (M-Base)|Thread 18/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:20.5202|Process 5856/ParameterUI (M-Base)|Thread 5/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:44.2536|Process 5856/ParameterUI (M-Base)|Thread 18/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:45.4796|Process 5856/ParameterUI (M-Base)|Thread 46/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:46.8506|Process 5856/ParameterUI (M-Base)|Thread 30/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:51:48.0726|Process 5856/ParameterUI (M-Base)|Thread 46/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:52:12.8695|Process 5856/ParameterUI (M-Base)|Thread 1/|FATAL|Symplex.BaseTypes.LanguageUtils|Symplex.BaseTypes.LanguageUtils.GetCultureSpecificFile|Translation file not found: C:\Symplex.NET\Keyboard_de.txt, fall back to english 
2025-07-31 08:52:17.2281|Process 5856/ParameterUI (M-Base)|Thread 89/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:52:18.4251|Process 5856/ParameterUI (M-Base)|Thread 113/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:52:19.0011|Process 5856/ParameterUI (M-Base)|Thread 27/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:52:19.4502|Process 5856/ParameterUI (M-Base)|Thread 56/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FR_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:52:19.4972|Process 5856/ParameterUI (M-Base)|Thread 94/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RC_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:52:19.7532|Process 5856/ParameterUI (M-Base)|Thread 107/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_RL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
2025-07-31 08:52:19.7712|Process 5856/ParameterUI (M-Base)|Thread 33/|FATAL|RealtimeInspDataCommunication|Symplex.ImageProcessing.InspectionDataCommunication.RealtimeInspDataCommunicationManager.ReportWaitTime|Inspection Communication Error: Station SW_FL_B waiting for SidewallDimension from (SW_FL_B, SW_FC_B, SW_FR_B, SW_RR_B, SW_RC_B, SW_RL_B) 
