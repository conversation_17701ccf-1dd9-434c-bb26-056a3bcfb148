#!/usr/bin/env python3
"""
Industrial Log Analyzer - Large Font Version
============================================
Optimized version with larger fonts for better readability
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import re
import os
from datetime import datetime, timedelta
import threading
from collections import defaultdict

class LargeFontLogAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Industrial Log Analyzer - Large Font Version")
        self.root.geometry("1800x1200")  # Even larger window
        
        # Configure larger fonts from the start
        self.setup_large_fonts()
        
        # Data storage
        self.log_data = {}
        self.log_directory = ""
        
        # Error patterns
        self.error_patterns = {
            'ethercat': r'EtherCAT|EtherCat|Ecat|CVZ\.|CBS\.|CSW\.',
            'camera': r'RC|RR|camera|Camera|Kamera|ImageProcessing|SidewallDimension',
            'motor': r'Motor|Servo|Antrieb|Achse|drive|enable|Enable|Amplifier',
            'safety': r'Schutztür|Protection|Safety|Sicherheit|#106[0-9]|door|Tür',
            'sensor': r'sensor|Sensor|verschmutzt|dirty|contamination|Lichtschranke|trigger',
            'flash_unit': r'Flash Unit|FlashUnit|230V|Beleuchtung|lighting|Licht|sync',
            'network': r'192\.168\.|network|Network|connection|Connection|timeout|Timeout',
            'communication': r'Communication|Kommunikation|NetMsg|Client|Server'
        }
        
        self.critical_codes = {
            '#1060': 'Motorspannung nicht verfügbar',
            '#1063': 'Schutztür offen',
            '#1067': 'Sicherheitskreis',
            '#1080': 'Auslösesensor verschmutzt',
            '#120': 'Lichtschranke verschmutzt'
        }
        
        self.setup_gui()
        
    def setup_large_fonts(self):
        """Configure extra large fonts for maximum readability"""
        import tkinter.font as tkFont
        
        # Create very large fonts
        self.large_font = tkFont.Font(family="Arial", size=14, weight="bold")
        self.text_font = tkFont.Font(family="Consolas", size=13)
        self.button_font = tkFont.Font(family="Arial", size=12, weight="bold")
        self.heading_font = tkFont.Font(family="Arial", size=16, weight="bold")
        
        # Configure matplotlib for larger fonts
        plt.rcParams.update({
            'font.size': 12,
            'axes.titlesize': 14,
            'axes.labelsize': 12,
            'xtick.labelsize': 11,
            'ytick.labelsize': 11,
            'legend.fontsize': 11
        })
        
    def setup_gui(self):
        """Setup GUI with large, readable fonts"""
        # Configure style
        style = ttk.Style()
        style.configure('Large.TLabel', font=self.large_font)
        style.configure('Large.TButton', font=self.button_font, padding=10)
        style.configure('Large.TLabelFrame.Label', font=self.heading_font)
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="📁 Log-Verzeichnis auswählen", 
                                   padding="15", style='Large.TLabelFrame')
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        self.directory_var = tk.StringVar()
        dir_entry = ttk.Entry(file_frame, textvariable=self.directory_var, 
                             width=60, font=self.text_font)
        dir_entry.grid(row=0, column=0, padx=(0, 10))
        
        browse_btn = ttk.Button(file_frame, text="📂 Durchsuchen", 
                               command=self.select_directory, style='Large.TButton')
        browse_btn.grid(row=0, column=1, padx=(0, 10))
        
        load_btn = ttk.Button(file_frame, text="📊 Logs laden", 
                             command=self.load_logs, style='Large.TButton')
        load_btn.grid(row=0, column=2)
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="🔍 Analyse-Steuerung", 
                                     padding="15", style='Large.TLabelFrame')
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # Date controls
        date_frame = ttk.Frame(control_frame)
        date_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(date_frame, text="📅 Startdatum:", style='Large.TLabel').grid(row=0, column=0, padx=(0, 10))
        self.start_date = tk.StringVar(value="2025-07-30")
        ttk.Entry(date_frame, textvariable=self.start_date, width=15, font=self.text_font).grid(row=0, column=1, padx=(0, 20))
        
        ttk.Label(date_frame, text="📅 Enddatum:", style='Large.TLabel').grid(row=0, column=2, padx=(0, 10))
        self.end_date = tk.StringVar(value="2025-07-31")
        ttk.Entry(date_frame, textvariable=self.end_date, width=15, font=self.text_font).grid(row=0, column=3)
        
        # Analysis buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="📈 Timeline-Analyse", 
                  command=self.timeline_analysis, style='Large.TButton').grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="🔗 Fehler-Korrelation", 
                  command=self.error_correlation, style='Large.TButton').grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="💚 System-Gesundheit", 
                  command=self.system_health_analysis, style='Large.TButton').grid(row=0, column=2, padx=(0, 10))
        ttk.Button(button_frame, text="📋 Bericht erstellen", 
                  command=self.generate_report, style='Large.TButton').grid(row=0, column=3)
        
        # Main notebook
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.create_tabs()
        
        # Status bar
        self.status_var = tk.StringVar(value="🟢 Bereit - Wählen Sie ein Log-Verzeichnis aus")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              font=self.large_font, relief=tk.SUNKEN, padding=10)
        status_bar.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(15, 0))
        
    def create_tabs(self):
        """Create notebook tabs with large fonts"""
        # Overview tab
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="📊 Übersicht")
        
        # Create horizontal layout
        left_frame = ttk.Frame(overview_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        right_frame = ttk.Frame(overview_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        overview_frame.columnconfigure(0, weight=1)
        overview_frame.columnconfigure(1, weight=2)
        overview_frame.rowconfigure(0, weight=1)
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(0, weight=1)
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=1)
        
        # Statistics text with large font
        stats_label = ttk.Label(left_frame, text="📈 System-Statistiken", style='Large.TLabel')
        stats_label.grid(row=0, column=0, sticky=(tk.W), pady=(0, 10))
        
        self.stats_text = scrolledtext.ScrolledText(left_frame, width=50, height=25, 
                                                   font=self.text_font, wrap=tk.WORD)
        self.stats_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Charts
        chart_label = ttk.Label(right_frame, text="📊 Fehlerverteilung", style='Large.TLabel')
        chart_label.grid(row=0, column=0, sticky=(tk.W), pady=(0, 10))
        
        self.overview_fig, self.overview_ax = plt.subplots(2, 1, figsize=(10, 12))
        self.overview_canvas = FigureCanvasTkAgg(self.overview_fig, right_frame)
        self.overview_canvas.get_tk_widget().grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        left_frame.rowconfigure(1, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # Analysis tab
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="🔍 Detailanalyse")
        
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, font=self.text_font, 
                                                      wrap=tk.WORD, width=100, height=30)
        self.analysis_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        
        analysis_frame.columnconfigure(0, weight=1)
        analysis_frame.rowconfigure(0, weight=1)
        
        # Log details tab
        details_frame = ttk.Frame(self.notebook)
        self.notebook.add(details_frame, text="📋 Log-Details")
        
        # Filter controls
        filter_frame = ttk.LabelFrame(details_frame, text="🔍 Filter", 
                                     padding="10", style='Large.TLabelFrame')
        filter_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(filter_frame, text="Log-Typ:", style='Large.TLabel').grid(row=0, column=0, padx=(0, 10))
        self.log_type_var = tk.StringVar()
        log_combo = ttk.Combobox(filter_frame, textvariable=self.log_type_var, 
                                width=25, font=self.text_font)
        log_combo.grid(row=0, column=1, padx=(0, 20))
        
        ttk.Label(filter_frame, text="Suche:", style='Large.TLabel').grid(row=0, column=2, padx=(0, 10))
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(filter_frame, textvariable=self.search_var, 
                                width=30, font=self.text_font)
        search_entry.grid(row=0, column=3, padx=(0, 20))
        
        ttk.Button(filter_frame, text="🔍 Filter anwenden", 
                  command=self.apply_filter, style='Large.TButton').grid(row=0, column=4)
        
        # Log details text
        self.details_text = scrolledtext.ScrolledText(details_frame, font=self.text_font, 
                                                     wrap=tk.NONE, width=120, height=25)
        self.details_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=10, pady=10)
        
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(1, weight=1)
        
    def select_directory(self):
        """Select log directory"""
        directory = filedialog.askdirectory(title="Log-Verzeichnis auswählen")
        if directory:
            self.directory_var.set(directory)
            self.log_directory = directory
            self.status_var.set(f"📁 Verzeichnis ausgewählt: {os.path.basename(directory)}")
            
    def load_logs(self):
        """Load logs with progress indication"""
        if not self.log_directory:
            messagebox.showerror("Fehler", "Bitte wählen Sie zuerst ein Log-Verzeichnis aus")
            return
            
        self.status_var.set("📊 Lade Log-Dateien...")
        thread = threading.Thread(target=self._load_logs_thread)
        thread.daemon = True
        thread.start()
        
    def _load_logs_thread(self):
        """Load logs in background thread"""
        try:
            self.log_data = {}
            log_files = []
            
            # Find all .log files
            for root, dirs, files in os.walk(self.log_directory):
                for file in files:
                    if file.endswith('.log'):
                        log_files.append(os.path.join(root, file))
            
            total_files = len(log_files)
            for i, log_file in enumerate(log_files):
                self.root.after(0, lambda i=i, t=total_files: 
                              self.status_var.set(f"📊 Lade Datei {i+1}/{t}..."))
                
                log_type = self._determine_log_type(log_file)
                if log_type not in self.log_data:
                    self.log_data[log_type] = []
                
                entries = self._parse_log_file(log_file)
                self.log_data[log_type].extend(entries)
            
            self.root.after(0, self._update_after_load)
            
        except Exception as e:
            self.root.after(0, lambda: self.status_var.set(f"❌ Fehler beim Laden: {str(e)}"))
            
    def _determine_log_type(self, filename):
        """Determine log type from filename"""
        basename = os.path.basename(filename).lower()
        
        if 'fatal' in basename:
            return 'Fatal Errors'
        elif 'malfunction' in basename:
            return 'Malfunctions'
        elif 'warning' in basename:
            return 'Warnings'
        elif 'health' in basename:
            return 'Health'
        elif 'error' in basename:
            return 'Errors'
        else:
            return 'Other'
            
    def _parse_log_file(self, filename):
        """Parse log file entries"""
        entries = []
        try:
            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+)', line)
                    if timestamp_match:
                        timestamp_str = timestamp_match.group(1)
                        try:
                            timestamp = datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')
                            message = line[len(timestamp_str):].strip('|').strip()
                            
                            entry = {
                                'timestamp': timestamp,
                                'message': message,
                                'file': os.path.basename(filename),
                                'category': self._categorize_error(message),
                                'severity': self._determine_severity(message)
                            }
                            entries.append(entry)
                        except ValueError:
                            continue
        except Exception as e:
            print(f"Fehler beim Parsen von {filename}: {e}")
            
        return entries
        
    def _categorize_error(self, message):
        """Categorize error based on message"""
        for category, pattern in self.error_patterns.items():
            if re.search(pattern, message, re.IGNORECASE):
                return category
        return 'other'
        
    def _determine_severity(self, message):
        """Determine error severity"""
        for code in self.critical_codes:
            if code in message:
                return 'critical'
                
        message_lower = message.lower()
        if any(word in message_lower for word in ['fatal', 'error', 'fehler']):
            return 'high'
        elif any(word in message_lower for word in ['warning', 'warnung']):
            return 'medium'
        return 'low'
        
    def _update_after_load(self):
        """Update GUI after loading"""
        total_entries = sum(len(entries) for entries in self.log_data.values())
        self.status_var.set(f"✅ {total_entries} Log-Einträge geladen")
        
        # Update combobox
        log_types = list(self.log_data.keys())
        for widget in self.notebook.winfo_children():
            for child in widget.winfo_children():
                if hasattr(child, 'winfo_children'):
                    for grandchild in child.winfo_children():
                        if isinstance(grandchild, ttk.Combobox):
                            grandchild['values'] = log_types
                            if log_types:
                                grandchild.set(log_types[0])
        
        self.update_overview()
        
    def update_overview(self):
        """Update overview with statistics and charts"""
        if not self.log_data:
            return
            
        # Calculate statistics
        total_entries = sum(len(entries) for entries in self.log_data.values())
        category_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        
        for entries in self.log_data.values():
            for entry in entries:
                category_counts[entry['category']] += 1
                severity_counts[entry['severity']] += 1
        
        # Update statistics text
        stats_content = f"SYSTEM-ÜBERSICHT\n{'='*60}\n\n"
        stats_content += f"📊 Gesamt-Einträge: {total_entries:,}\n\n"
        
        stats_content += "📂 Log-Typen:\n" + "-" * 30 + "\n"
        for log_type, entries in sorted(self.log_data.items(), key=lambda x: len(x[1]), reverse=True):
            stats_content += f"  {log_type}: {len(entries):,} Einträge\n"
        
        stats_content += "\n🏷️ Fehler-Kategorien:\n" + "-" * 30 + "\n"
        for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
            stats_content += f"  {category.title()}: {count:,}\n"
        
        stats_content += "\n⚠️ Schweregrad-Verteilung:\n" + "-" * 30 + "\n"
        severity_icons = {'critical': '🔴', 'high': '🟠', 'medium': '🟡', 'low': '🟢'}
        for severity, count in sorted(severity_counts.items(), key=lambda x: x[1], reverse=True):
            icon = severity_icons.get(severity, '⚪')
            stats_content += f"  {icon} {severity.title()}: {count:,}\n"
        
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, stats_content)
        
        # Update charts
        self.overview_ax[0].clear()
        self.overview_ax[1].clear()
        
        if category_counts:
            categories = list(category_counts.keys())[:8]  # Top 8 categories
            counts = [category_counts[cat] for cat in categories]
            
            self.overview_ax[0].pie(counts, labels=categories, autopct='%1.1f%%', 
                                   textprops={'fontsize': 11})
            self.overview_ax[0].set_title('Fehler-Kategorien Verteilung', 
                                         fontsize=14, fontweight='bold', pad=20)
        
        if severity_counts:
            severities = list(severity_counts.keys())
            counts = list(severity_counts.values())
            colors = {'critical': '#FF4444', 'high': '#FF8800', 'medium': '#FFDD00', 'low': '#44AA44'}
            bar_colors = [colors.get(s, '#4488CC') for s in severities]
            
            bars = self.overview_ax[1].bar(severities, counts, color=bar_colors, alpha=0.8)
            self.overview_ax[1].set_title('Schweregrad-Verteilung', 
                                         fontsize=14, fontweight='bold', pad=20)
            self.overview_ax[1].set_ylabel('Anzahl', fontsize=12)
            
            # Add value labels on bars
            for bar in bars:
                height = bar.get_height()
                self.overview_ax[1].text(bar.get_x() + bar.get_width()/2., height,
                                        f'{int(height):,}', ha='center', va='bottom', fontsize=11)
        
        plt.tight_layout()
        self.overview_canvas.draw()
        
    def timeline_analysis(self):
        """Simplified timeline analysis"""
        if not self.log_data:
            messagebox.showwarning("Warnung", "Bitte laden Sie zuerst Log-Dateien")
            return
            
        self.status_var.set("📈 Führe Timeline-Analyse durch...")
        
        # Simple analysis message
        analysis_content = "TIMELINE-ANALYSE\n"
        analysis_content += "=" * 60 + "\n\n"
        analysis_content += "Die Timeline-Analyse wird in der Vollversion implementiert.\n\n"
        analysis_content += "Verfügbare Features:\n"
        analysis_content += "• Chronologische Fehlerverteilung\n"
        analysis_content += "• Zeitbasierte Korrelationen\n"
        analysis_content += "• Muster-Erkennung\n"
        
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, analysis_content)
        self.notebook.select(1)
        
        self.status_var.set("✅ Timeline-Analyse abgeschlossen")
        
    def error_correlation(self):
        """Simplified correlation analysis"""
        if not self.log_data:
            messagebox.showwarning("Warnung", "Bitte laden Sie zuerst Log-Dateien")
            return
            
        self.status_var.set("🔗 Analysiere Fehler-Korrelationen...")
        
        analysis_content = "FEHLER-KORRELATIONS-ANALYSE\n"
        analysis_content += "=" * 60 + "\n\n"
        
        # Basic correlation analysis
        try:
            start_date = datetime.strptime(self.start_date.get(), '%Y-%m-%d')
            end_date = datetime.strptime(self.end_date.get(), '%Y-%m-%d') + timedelta(days=1)
            
            # Collect entries in date range
            all_entries = []
            for entries in self.log_data.values():
                for entry in entries:
                    if start_date <= entry['timestamp'] < end_date:
                        all_entries.append(entry)
            
            all_entries.sort(key=lambda x: x['timestamp'])
            
            analysis_content += f"Analysiert: {len(all_entries)} Einträge\n"
            analysis_content += f"Zeitraum: {self.start_date.get()} bis {self.end_date.get()}\n\n"
            
            # Find critical sequences
            critical_events = [e for e in all_entries if e['severity'] == 'critical']
            
            if critical_events:
                analysis_content += f"🔴 Kritische Ereignisse gefunden: {len(critical_events)}\n\n"
                
                for i, event in enumerate(critical_events[:5], 1):  # Show first 5
                    analysis_content += f"{i}. {event['timestamp']}\n"
                    analysis_content += f"   Kategorie: {event['category']}\n"
                    analysis_content += f"   Nachricht: {event['message'][:100]}...\n\n"
            else:
                analysis_content += "✅ Keine kritischen Ereignisse im Zeitraum gefunden\n"
                
        except ValueError:
            analysis_content += "❌ Ungültiges Datumsformat. Verwenden Sie YYYY-MM-DD\n"
        
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, analysis_content)
        self.notebook.select(1)
        
        self.status_var.set("✅ Korrelations-Analyse abgeschlossen")
        
    def system_health_analysis(self):
        """Simplified health analysis"""
        if not self.log_data:
            messagebox.showwarning("Warnung", "Bitte laden Sie zuerst Log-Dateien")
            return
            
        self.status_var.set("💚 Analysiere System-Gesundheit...")
        
        analysis_content = "SYSTEM-GESUNDHEITS-ANALYSE\n"
        analysis_content += "=" * 60 + "\n\n"
        
        # Calculate basic health metrics
        total_entries = sum(len(entries) for entries in self.log_data.values())
        severity_counts = defaultdict(int)
        category_counts = defaultdict(int)
        
        for entries in self.log_data.values():
            for entry in entries:
                severity_counts[entry['severity']] += 1
                category_counts[entry['category']] += 1
        
        # Simple health score
        critical_count = severity_counts.get('critical', 0)
        high_count = severity_counts.get('high', 0)
        health_score = max(0, 100 - (critical_count * 5) - (high_count * 2))
        
        analysis_content += f"🎯 System-Gesundheitsbewertung: {health_score:.0f}/100\n\n"
        
        if health_score >= 90:
            analysis_content += "✅ System-Status: AUSGEZEICHNET\n"
        elif health_score >= 70:
            analysis_content += "🟡 System-Status: GUT\n"
        elif health_score >= 50:
            analysis_content += "🟠 System-Status: BEDARF AUFMERKSAMKEIT\n"
        else:
            analysis_content += "🔴 System-Status: KRITISCH\n"
        
        analysis_content += f"\n📊 Fehler-Zusammenfassung:\n"
        analysis_content += f"   🔴 Kritisch: {critical_count}\n"
        analysis_content += f"   🟠 Hoch: {high_count}\n"
        analysis_content += f"   🟡 Mittel: {severity_counts.get('medium', 0)}\n"
        analysis_content += f"   🟢 Niedrig: {severity_counts.get('low', 0)}\n\n"
        
        # Top problematic components
        if category_counts:
            analysis_content += "🔧 Problematischste Komponenten:\n"
            sorted_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)
            for i, (category, count) in enumerate(sorted_categories[:5], 1):
                analysis_content += f"   {i}. {category.title()}: {count} Probleme\n"
        
        # Recommendations
        analysis_content += "\n💡 Empfehlungen:\n"
        if critical_count > 0:
            analysis_content += "   • Kritische Fehler sofort beheben\n"
        if high_count > 10:
            analysis_content += "   • Präventive Wartung durchführen\n"
        if health_score < 70:
            analysis_content += "   • System-Überwachung verstärken\n"
        if critical_count == 0 and high_count < 5:
            analysis_content += "   • System läuft stabil, Routinewartung fortsetzen\n"
        
        self.analysis_text.delete(1.0, tk.END)
        self.analysis_text.insert(tk.END, analysis_content)
        self.notebook.select(1)
        
        self.status_var.set("✅ Gesundheits-Analyse abgeschlossen")
        
    def apply_filter(self):
        """Apply filters to log view"""
        if not self.log_data:
            return
            
        log_type = self.log_type_var.get()
        search_term = self.search_var.get().lower()
        
        if not log_type or log_type not in self.log_data:
            self.details_text.delete(1.0, tk.END)
            self.details_text.insert(tk.END, "Bitte wählen Sie einen gültigen Log-Typ aus.")
            return
        
        entries = self.log_data[log_type]
        
        if search_term:
            entries = [e for e in entries if search_term in e['message'].lower()]
        
        # Sort by timestamp
        entries.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # Display filtered entries
        content = f"GEFILTERTE LOG-ANSICHT: {log_type}\n"
        content += f"Suchbegriff: '{search_term}'\n" if search_term else "Kein Suchfilter\n"
        content += f"Gefundene Einträge: {len(entries)}\n"
        content += "=" * 80 + "\n\n"
        
        for entry in entries[:500]:  # Show first 500 entries
            content += f"[{entry['timestamp']}] [{entry['severity'].upper()}] [{entry['category'].upper()}]\n"
            content += f"Datei: {entry['file']}\n"
            content += f"Nachricht: {entry['message']}\n"
            content += "-" * 80 + "\n"
        
        if len(entries) > 500:
            content += f"\n... und {len(entries) - 500} weitere Einträge (nicht angezeigt)\n"
        
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(tk.END, content)
        self.notebook.select(2)
        
    def generate_report(self):
        """Generate a simple report"""
        if not self.log_data:
            messagebox.showwarning("Warnung", "Bitte laden Sie zuerst Log-Dateien")
            return
            
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text-Dateien", "*.txt"), ("Alle Dateien", "*.*")],
            title="Bericht speichern unter"
        )
        
        if not filename:
            return
            
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("INDUSTRIAL LOG ANALYZER - SYSTEM-BERICHT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Erstellt am: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}\n")
                f.write(f"Analysezeitraum: {self.start_date.get()} bis {self.end_date.get()}\n")
                f.write("=" * 80 + "\n\n")
                
                f.write(self.stats_text.get(1.0, tk.END))
                f.write("\n" + "=" * 80 + "\n\n")
                f.write(self.analysis_text.get(1.0, tk.END))
                
            messagebox.showinfo("Erfolg", f"Bericht gespeichert unter:\n{filename}")
            self.status_var.set(f"✅ Bericht gespeichert: {os.path.basename(filename)}")
            
        except Exception as e:
            messagebox.showerror("Fehler", f"Fehler beim Speichern: {str(e)}")

def main():
    """Main application entry point"""
    root = tk.Tk()
    app = LargeFontLogAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
