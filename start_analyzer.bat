@echo off
echo.
echo ========================================
echo   Industrial Log Analyzer
echo   TwinCAT/EtherCat System Analysis
echo ========================================
echo.

:: Check if Python is available
py --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found!
    echo Please install Python from python.org
    pause
    exit /b 1
)

:: Check if required packages are installed
echo Checking dependencies...
py -c "import pandas, matplotlib, numpy, tkinter" >nul 2>&1
if errorlevel 1 (
    echo Installing missing dependencies...
    py install_dependencies.py
    if errorlevel 1 (
        echo Error installing dependencies!
        pause
        exit /b 1
    )
)

:: Offer configuration selection
echo.
echo Select Log Analyzer version:
echo 1. Basic Log Analyzer (recommended for first use)
echo 2. Advanced Log Analyzer (full features)
echo 3. Large Font Version (better readability)
echo.
set /p choice="Enter your choice (1, 2, or 3): "

if "%choice%"=="1" (
    echo.
    echo Starting Basic Log Analyzer...
    py log_analyzer.py
) else if "%choice%"=="2" (
    echo.
    echo Starting Advanced Log Analyzer...
    py advanced_log_analyzer.py
) else if "%choice%"=="3" (
    echo.
    echo Starting Large Font Log Analyzer...
    py log_analyzer_large_font.py
) else (
    echo.
    echo Invalid choice. Starting Large Font Version for better readability...
    py log_analyzer_large_font.py
)

if errorlevel 1 (
    echo.
    echo Error starting the application!
    echo Please check the error messages above.
    pause
)
