#!/usr/bin/env python3
"""
Industrial Log Analyzer
========================
Comprehensive log analysis tool for TwinCAT/EtherCat industrial systems.
Analyzes various log types including Fatal Errors, Malfunctions, Warnings, Health logs, etc.

Author: Log Analysis System
Date: July 31, 2025
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import re
import os
import glob
from datetime import datetime, timedelta
import threading
from collections import defaultdict, Counter
import json

class LogAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("Industrial Log Analyzer - TwinCAT/EtherCat System")
        self.root.geometry("1800x1200")  # Even larger window for bigger fonts
        
        # Data storage
        self.log_data = {}
        self.analysis_results = {}
        self.log_directory = ""
        
        # Error patterns for different log types
        self.error_patterns = {
            'ethercat': r'EtherCAT|EtherCat|Ecat|CVZ\.|CBS\.|CSW\.',
            'camera': r'RC|RR|camera|Camera|Kamera|ImageProcessing|SidewallDimension',
            'motor': r'Motor|Servo|Antrieb|Achse|drive|enable|Enable|Amplifier',
            'safety': r'Schutztür|Protection|Safety|Sicherheit|#106[0-9]|door|Tür',
            'sensor': r'sensor|Sensor|verschmutzt|dirty|contamination|Lichtschranke|trigger',
            'flash_unit': r'Flash Unit|FlashUnit|230V|Beleuchtung|lighting|Licht|sync',
            'network': r'192\.168\.|network|Network|connection|Connection|timeout|Timeout',
            'communication': r'Communication|Kommunikation|NetMsg|Client|Server'
        }
        
        # Critical error codes
        self.critical_codes = {
            '#1060': 'Motorspannung nicht verfügbar',
            '#1063': 'Schutztür offen',
            '#1067': 'Sicherheitskreis',
            '#1080': 'Auslösesensor verschmutzt',
            '#120': 'Lichtschranke verschmutzt'
        }
        
        self.setup_gui()
        
    def setup_fonts(self):
        """Configure larger fonts for better readability"""
        import tkinter.font as tkFont
        
        # Create much larger fonts
        self.default_font = tkFont.nametofont("TkDefaultFont")
        self.default_font.configure(size=14)
        
        self.heading_font = tkFont.Font(family="Arial", size=16, weight="bold")
        self.text_font = tkFont.Font(family="Consolas", size=13)
        self.large_text_font = tkFont.Font(family="Consolas", size=14)
        
        # Configure default fonts
        self.root.option_add("*Font", self.default_font)
        
    def setup_gui(self):
        """Setup the main GUI interface"""
        # Configure font sizes for better readability
        self.setup_fonts()
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # File selection frame
        file_frame = ttk.LabelFrame(main_frame, text="Log Directory Selection", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.directory_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.directory_var, width=80).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(file_frame, text="Browse", command=self.select_directory).grid(row=0, column=1, padx=(5, 0))
        ttk.Button(file_frame, text="Load Logs", command=self.load_logs).grid(row=0, column=2, padx=(10, 0))
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Analysis Controls", padding="5")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Date range selection
        ttk.Label(control_frame, text="Start Date:").grid(row=0, column=0, padx=(0, 5))
        self.start_date = tk.StringVar(value="2025-07-30")
        ttk.Entry(control_frame, textvariable=self.start_date, width=12).grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(control_frame, text="End Date:").grid(row=0, column=2, padx=(0, 5))
        self.end_date = tk.StringVar(value="2025-07-31")
        ttk.Entry(control_frame, textvariable=self.end_date, width=12).grid(row=0, column=3, padx=(0, 20))
        
        # Analysis buttons
        ttk.Button(control_frame, text="Timeline Analysis", command=self.timeline_analysis).grid(row=0, column=4, padx=(0, 5))
        ttk.Button(control_frame, text="Error Correlation", command=self.error_correlation).grid(row=0, column=5, padx=(0, 5))
        ttk.Button(control_frame, text="System Health", command=self.system_health_analysis).grid(row=0, column=6, padx=(0, 5))
        ttk.Button(control_frame, text="Generate Report", command=self.generate_report).grid(row=0, column=7, padx=(0, 5))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create tabs
        self.create_overview_tab()
        self.create_timeline_tab()
        self.create_analysis_tab()
        self.create_detailed_view_tab()
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def create_overview_tab(self):
        """Create overview tab with summary statistics"""
        overview_frame = ttk.Frame(self.notebook)
        self.notebook.add(overview_frame, text="Overview")
        
        # Left panel - statistics
        stats_frame = ttk.LabelFrame(overview_frame, text="System Statistics", padding="10")
        stats_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, width=50, height=25, font=self.large_text_font)
        self.stats_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Right panel - error distribution chart
        chart_frame = ttk.LabelFrame(overview_frame, text="Error Distribution", padding="10")
        chart_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        # Configure grid weights
        overview_frame.columnconfigure(0, weight=1)
        overview_frame.columnconfigure(1, weight=2)
        overview_frame.rowconfigure(0, weight=1)
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.rowconfigure(0, weight=1)
        chart_frame.columnconfigure(0, weight=1)
        chart_frame.rowconfigure(0, weight=1)
        
        # Matplotlib figure for charts
        plt.rcParams.update({'font.size': 14})  # Much larger chart font size
        self.overview_fig, self.overview_ax = plt.subplots(2, 1, figsize=(8, 10))
        self.overview_canvas = FigureCanvasTkAgg(self.overview_fig, chart_frame)
        self.overview_canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def create_timeline_tab(self):
        """Create timeline analysis tab"""
        timeline_frame = ttk.Frame(self.notebook)
        self.notebook.add(timeline_frame, text="Timeline Analysis")
        
        # Timeline chart
        self.timeline_fig, self.timeline_ax = plt.subplots(figsize=(12, 8))
        # Set much larger font for timeline charts
        self.timeline_ax.tick_params(labelsize=12)
        self.timeline_canvas = FigureCanvasTkAgg(self.timeline_fig, timeline_frame)
        self.timeline_canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        timeline_frame.columnconfigure(0, weight=1)
        timeline_frame.rowconfigure(0, weight=1)
        
    def create_analysis_tab(self):
        """Create detailed analysis tab"""
        analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(analysis_frame, text="Detailed Analysis")
        
        # Create sub-notebook for different analysis types
        analysis_notebook = ttk.Notebook(analysis_frame)
        analysis_notebook.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Error correlation tab
        correlation_frame = ttk.Frame(analysis_notebook)
        analysis_notebook.add(correlation_frame, text="Error Correlation")
        
        self.correlation_text = scrolledtext.ScrolledText(correlation_frame, width=100, height=30, font=self.large_text_font)
        self.correlation_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # System health tab
        health_frame = ttk.Frame(analysis_notebook)
        analysis_notebook.add(health_frame, text="System Health")
        
        self.health_text = scrolledtext.ScrolledText(health_frame, width=100, height=30, font=self.large_text_font)
        self.health_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure weights
        analysis_frame.columnconfigure(0, weight=1)
        analysis_frame.rowconfigure(0, weight=1)
        correlation_frame.columnconfigure(0, weight=1)
        correlation_frame.rowconfigure(0, weight=1)
        health_frame.columnconfigure(0, weight=1)
        health_frame.rowconfigure(0, weight=1)
        
    def create_detailed_view_tab(self):
        """Create detailed log view tab"""
        detailed_frame = ttk.Frame(self.notebook)
        self.notebook.add(detailed_frame, text="Log Details")
        
        # Filter frame
        filter_frame = ttk.LabelFrame(detailed_frame, text="Filters", padding="5")
        filter_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        ttk.Label(filter_frame, text="Log Type:").grid(row=0, column=0, padx=(0, 5))
        self.log_type_var = tk.StringVar()
        log_type_combo = ttk.Combobox(filter_frame, textvariable=self.log_type_var, width=20)
        log_type_combo.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(filter_frame, text="Search:").grid(row=0, column=2, padx=(0, 5))
        self.search_var = tk.StringVar()
        ttk.Entry(filter_frame, textvariable=self.search_var, width=30).grid(row=0, column=3, padx=(0, 10))
        
        ttk.Button(filter_frame, text="Apply Filter", command=self.apply_filter).grid(row=0, column=4)
        
        # Detailed log view
        self.detailed_text = scrolledtext.ScrolledText(detailed_frame, width=150, height=30, font=self.text_font)
        self.detailed_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure weights
        detailed_frame.columnconfigure(0, weight=1)
        detailed_frame.rowconfigure(1, weight=1)
        
    def select_directory(self):
        """Select log directory"""
        directory = filedialog.askdirectory(title="Select Log Directory")
        if directory:
            self.directory_var.set(directory)
            self.log_directory = directory
            
    def load_logs(self):
        """Load all log files from the selected directory"""
        if not self.log_directory:
            messagebox.showerror("Error", "Please select a log directory first")
            return
            
        self.status_var.set("Loading log files...")
        self.root.update()
        
        # Run in separate thread to prevent GUI freezing
        thread = threading.Thread(target=self._load_logs_thread)
        thread.daemon = True
        thread.start()
        
    def _load_logs_thread(self):
        """Load logs in separate thread"""
        try:
            self.log_data = {}
            
            # Find all log files
            log_files = []
            for root, dirs, files in os.walk(self.log_directory):
                for file in files:
                    if file.endswith('.log'):
                        log_files.append(os.path.join(root, file))
            
            total_files = len(log_files)
            for i, log_file in enumerate(log_files):
                # Update status
                self.root.after(0, lambda: self.status_var.set(f"Loading {i+1}/{total_files}: {os.path.basename(log_file)}"))
                
                # Determine log type from filename
                log_type = self._determine_log_type(log_file)
                
                if log_type not in self.log_data:
                    self.log_data[log_type] = []
                
                # Parse log file
                entries = self._parse_log_file(log_file)
                self.log_data[log_type].extend(entries)
                
            # Update GUI in main thread
            self.root.after(0, self._update_gui_after_load)
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Error loading logs: {str(e)}"))
            self.root.after(0, lambda: self.status_var.set("Error loading logs"))
            
    def _determine_log_type(self, filename):
        """Determine log type from filename"""
        basename = os.path.basename(filename).lower()
        
        if 'fatal' in basename:
            return 'Fatal Errors'
        elif 'malfunction' in basename:
            return 'Malfunctions'
        elif 'warning' in basename:
            return 'Warnings'
        elif 'health' in basename:
            return 'Health'
        elif 'filebackup' in basename:
            return 'FileBackup'
        elif 'useractivity' in basename:
            return 'UserActivity'
        elif 'tracking' in basename:
            return 'Tracking'
        elif 'grabber' in basename:
            return 'Grabber'
        elif 'error' in basename:
            return 'Errors'
        else:
            return 'Other'
            
    def _parse_log_file(self, filename):
        """Parse individual log file"""
        entries = []
        
        try:
            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    # Extract timestamp and message
                    timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+)', line)
                    if timestamp_match:
                        timestamp_str = timestamp_match.group(1)
                        try:
                            timestamp = datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')
                            message = line[len(timestamp_str):].strip('|').strip()
                            
                            entry = {
                                'timestamp': timestamp,
                                'message': message,
                                'file': os.path.basename(filename),
                                'line': line_num,
                                'raw_line': line
                            }
                            
                            # Categorize error
                            entry['category'] = self._categorize_error(message)
                            entry['severity'] = self._determine_severity(message)
                            
                            entries.append(entry)
                        except ValueError:
                            continue
                            
        except Exception as e:
            print(f"Error parsing {filename}: {e}")
            
        return entries
        
    def _categorize_error(self, message):
        """Categorize error based on message content"""
        message_lower = message.lower()
        
        for category, pattern in self.error_patterns.items():
            if re.search(pattern, message, re.IGNORECASE):
                return category
                
        return 'other'
        
    def _determine_severity(self, message):
        """Determine error severity"""
        message_lower = message.lower()
        
        # Check for critical error codes
        for code in self.critical_codes:
            if code in message:
                return 'critical'
                
        if any(word in message_lower for word in ['fatal', 'error', 'fehler', 'fail']):
            return 'high'
        elif any(word in message_lower for word in ['warning', 'warnung', 'caution']):
            return 'medium'
        else:
            return 'low'
            
    def _update_gui_after_load(self):
        """Update GUI after logs are loaded"""
        self.status_var.set(f"Loaded {sum(len(entries) for entries in self.log_data.values())} log entries")
        
        # Update log type combobox
        if hasattr(self, 'log_type_var'):
            log_types = list(self.log_data.keys())
            # Find the combobox widget and update its values
            for widget in self.notebook.winfo_children():
                if widget.winfo_class() == 'Frame':
                    for child in widget.winfo_children():
                        if child.winfo_class() == 'Labelframe':
                            for grandchild in child.winfo_children():
                                if grandchild.winfo_class() == 'Combobox':
                                    grandchild['values'] = log_types
                                    if log_types:
                                        grandchild.set(log_types[0])
                                    break
        
        # Generate initial overview
        self.update_overview()
        
    def update_overview(self):
        """Update overview statistics"""
        if not self.log_data:
            return
            
        # Calculate statistics
        stats = []
        total_entries = 0
        category_counts = defaultdict(int)
        severity_counts = defaultdict(int)
        
        for log_type, entries in self.log_data.items():
            count = len(entries)
            total_entries += count
            stats.append(f"{log_type}: {count} entries")
            
            for entry in entries:
                category_counts[entry['category']] += 1
                severity_counts[entry['severity']] += 1
        
        # Update statistics text
        stats_content = f"SYSTEM LOG OVERVIEW\n{'='*50}\n\n"
        stats_content += f"Total Log Entries: {total_entries}\n\n"
        stats_content += "Log Types:\n" + "-" * 20 + "\n"
        stats_content += "\n".join(stats) + "\n\n"
        
        stats_content += "Error Categories:\n" + "-" * 20 + "\n"
        for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
            stats_content += f"{category.capitalize()}: {count}\n"
        
        stats_content += "\nSeverity Distribution:\n" + "-" * 20 + "\n"
        for severity, count in sorted(severity_counts.items(), key=lambda x: x[1], reverse=True):
            stats_content += f"{severity.capitalize()}: {count}\n"
        
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, stats_content)
        
        # Update charts
        self.overview_ax[0].clear()
        self.overview_ax[1].clear()
        
        # Category distribution pie chart
        if category_counts:
            categories = list(category_counts.keys())
            counts = list(category_counts.values())
            self.overview_ax[0].pie(counts, labels=categories, autopct='%1.1f%%', textprops={'fontsize': 12})
            self.overview_ax[0].set_title('Error Categories Distribution', fontsize=16, fontweight='bold')
        
        # Severity bar chart
        if severity_counts:
            severities = list(severity_counts.keys())
            counts = list(severity_counts.values())
            colors = {'critical': 'red', 'high': 'orange', 'medium': 'yellow', 'low': 'green'}
            bar_colors = [colors.get(s, 'blue') for s in severities]
            self.overview_ax[1].bar(severities, counts, color=bar_colors)
            self.overview_ax[1].set_title('Severity Distribution', fontsize=16, fontweight='bold')
            self.overview_ax[1].set_ylabel('Count', fontsize=12)
            self.overview_ax[1].tick_params(labelsize=11)
        
        self.overview_canvas.draw()
        
    def timeline_analysis(self):
        """Perform timeline analysis"""
        if not self.log_data:
            messagebox.showwarning("Warning", "Please load log files first")
            return
            
        try:
            start_date = datetime.strptime(self.start_date.get(), '%Y-%m-%d')
            end_date = datetime.strptime(self.end_date.get(), '%Y-%m-%d') + timedelta(days=1)
        except ValueError:
            messagebox.showerror("Error", "Invalid date format. Use YYYY-MM-DD")
            return
            
        self.status_var.set("Generating timeline analysis...")
        
        # Collect all entries in date range
        timeline_entries = []
        for log_type, entries in self.log_data.items():
            for entry in entries:
                if start_date <= entry['timestamp'] < end_date:
                    timeline_entries.append(entry)
        
        # Sort by timestamp
        timeline_entries.sort(key=lambda x: x['timestamp'])
        
        # Create timeline plot
        self.timeline_ax.clear()
        
        if timeline_entries:
            timestamps = [entry['timestamp'] for entry in timeline_entries]
            categories = [entry['category'] for entry in timeline_entries]
            severities = [entry['severity'] for entry in timeline_entries]
            
            # Create scatter plot with different colors for severity
            severity_colors = {'critical': 'red', 'high': 'orange', 'medium': 'yellow', 'low': 'green'}
            
            for severity in set(severities):
                severity_timestamps = [ts for ts, sev in zip(timestamps, severities) if sev == severity]
                severity_categories = [cat for cat, sev in zip(categories, severities) if sev == severity]
                
                # Map categories to y-positions
                unique_categories = list(set(categories))
                y_positions = [unique_categories.index(cat) for cat in severity_categories]
                
                self.timeline_ax.scatter(severity_timestamps, y_positions, 
                                       c=severity_colors.get(severity, 'blue'), 
                                       label=severity, alpha=0.7, s=30)
            
            self.timeline_ax.set_yticks(range(len(unique_categories)))
            self.timeline_ax.set_yticklabels(unique_categories, fontsize=12)
            self.timeline_ax.set_xlabel('Time', fontsize=14)
            self.timeline_ax.set_ylabel('Error Category', fontsize=14)
            self.timeline_ax.set_title(f'Timeline Analysis ({self.start_date.get()} to {self.end_date.get()})', 
                                      fontsize=16, fontweight='bold')
            self.timeline_ax.legend(fontsize=12)
            self.timeline_ax.grid(True, alpha=0.3)
            
            # Format x-axis
            self.timeline_fig.autofmt_xdate()
        
        self.timeline_canvas.draw()
        self.status_var.set("Timeline analysis complete")
        
        # Switch to timeline tab
        self.notebook.select(1)
        
    def error_correlation(self):
        """Analyze error correlations and patterns"""
        if not self.log_data:
            messagebox.showwarning("Warning", "Please load log files first")
            return
            
        self.status_var.set("Analyzing error correlations...")
        
        try:
            start_date = datetime.strptime(self.start_date.get(), '%Y-%m-%d')
            end_date = datetime.strptime(self.end_date.get(), '%Y-%m-%d') + timedelta(days=1)
        except ValueError:
            messagebox.showerror("Error", "Invalid date format. Use YYYY-MM-DD")
            return
            
        # Collect entries in date range
        entries = []
        for log_type, log_entries in self.log_data.items():
            for entry in log_entries:
                if start_date <= entry['timestamp'] < end_date:
                    entries.append(entry)
        
        entries.sort(key=lambda x: x['timestamp'])
        
        # Analyze correlations
        correlation_report = "ERROR CORRELATION ANALYSIS\n"
        correlation_report += "=" * 60 + "\n\n"
        
        # Time-based correlation analysis
        correlation_report += "Time-based Error Correlations:\n"
        correlation_report += "-" * 40 + "\n"
        
        # Look for errors within 5-minute windows
        time_window = timedelta(minutes=5)
        correlations = defaultdict(list)
        
        for i, entry in enumerate(entries):
            window_entries = []
            for j, other_entry in enumerate(entries[i:i+20], i):  # Look at next 20 entries
                if other_entry['timestamp'] - entry['timestamp'] <= time_window:
                    if other_entry['category'] != entry['category']:
                        window_entries.append(other_entry)
                else:
                    break
            
            if window_entries:
                key = f"{entry['category']} -> {', '.join(set(e['category'] for e in window_entries))}"
                correlations[key].append(entry['timestamp'])
        
        # Report significant correlations
        for correlation, timestamps in sorted(correlations.items(), key=lambda x: len(x[1]), reverse=True):
            if len(timestamps) >= 2:  # Only show if it happened multiple times
                correlation_report += f"\n{correlation}:\n"
                correlation_report += f"  Occurrences: {len(timestamps)}\n"
                correlation_report += f"  First: {min(timestamps)}\n"
                correlation_report += f"  Last: {max(timestamps)}\n"
        
        # Critical error sequence analysis
        correlation_report += "\n\nCritical Error Sequences:\n"
        correlation_report += "-" * 40 + "\n"
        
        critical_sequences = []
        for i, entry in enumerate(entries):
            if entry['severity'] == 'critical':
                # Look for what happened before and after
                before = entries[max(0, i-5):i]
                after = entries[i+1:i+6]
                
                sequence = {
                    'critical_event': entry,
                    'before': before,
                    'after': after
                }
                critical_sequences.append(sequence)
        
        for seq in critical_sequences:
            correlation_report += f"\nCritical Event at {seq['critical_event']['timestamp']}:\n"
            correlation_report += f"  Event: {seq['critical_event']['message'][:100]}...\n"
            
            if seq['before']:
                correlation_report += "  Preceding events:\n"
                for event in seq['before'][-3:]:  # Last 3 before
                    correlation_report += f"    {event['timestamp']}: {event['category']} - {event['message'][:80]}...\n"
                    
            if seq['after']:
                correlation_report += "  Following events:\n"
                for event in seq['after'][:3]:  # First 3 after
                    correlation_report += f"    {event['timestamp']}: {event['category']} - {event['message'][:80]}...\n"
        
        # System component interaction analysis
        correlation_report += "\n\nSystem Component Interactions:\n"
        correlation_report += "-" * 40 + "\n"
        
        component_interactions = defaultdict(int)
        for i in range(len(entries) - 1):
            curr_category = entries[i]['category']
            next_category = entries[i + 1]['category']
            if curr_category != next_category:
                time_diff = entries[i + 1]['timestamp'] - entries[i]['timestamp']
                if time_diff <= timedelta(minutes=2):
                    component_interactions[f"{curr_category} -> {next_category}"] += 1
        
        for interaction, count in sorted(component_interactions.items(), key=lambda x: x[1], reverse=True)[:10]:
            correlation_report += f"{interaction}: {count} times\n"
        
        self.correlation_text.delete(1.0, tk.END)
        self.correlation_text.insert(tk.END, correlation_report)
        
        self.status_var.set("Error correlation analysis complete")
        
        # Switch to analysis tab
        self.notebook.select(2)
        
    def system_health_analysis(self):
        """Analyze overall system health"""
        if not self.log_data:
            messagebox.showwarning("Warning", "Please load log files first")
            return
            
        self.status_var.set("Analyzing system health...")
        
        try:
            start_date = datetime.strptime(self.start_date.get(), '%Y-%m-%d')
            end_date = datetime.strptime(self.end_date.get(), '%Y-%m-%d') + timedelta(days=1)
        except ValueError:
            messagebox.showerror("Error", "Invalid date format. Use YYYY-MM-DD")
            return
            
        health_report = "SYSTEM HEALTH ANALYSIS\n"
        health_report += "=" * 60 + "\n\n"
        
        # Collect health metrics
        total_errors = 0
        system_uptime_issues = []
        component_health = defaultdict(lambda: {'errors': 0, 'warnings': 0, 'last_issue': None})
        
        for log_type, entries in self.log_data.items():
            for entry in entries:
                if start_date <= entry['timestamp'] < end_date:
                    total_errors += 1
                    component = entry['category']
                    
                    if entry['severity'] in ['critical', 'high']:
                        component_health[component]['errors'] += 1
                    else:
                        component_health[component]['warnings'] += 1
                        
                    component_health[component]['last_issue'] = entry['timestamp']
                    
                    # Track system uptime issues
                    if any(keyword in entry['message'].lower() for keyword in 
                          ['system down', 'system stopped', 'fatal', 'crash', 'restart']):
                        system_uptime_issues.append(entry)
        
        # Overall health score calculation
        health_score = max(0, 100 - (total_errors / 10))  # Simple scoring
        
        health_report += f"Overall Health Score: {health_score:.1f}/100\n"
        health_report += f"Analysis Period: {self.start_date.get()} to {self.end_date.get()}\n"
        health_report += f"Total Issues Found: {total_errors}\n\n"
        
        # Component health breakdown
        health_report += "Component Health Status:\n"
        health_report += "-" * 40 + "\n"
        
        for component, health in sorted(component_health.items(), 
                                      key=lambda x: x[1]['errors'] + x[1]['warnings'], 
                                      reverse=True):
            total_issues = health['errors'] + health['warnings']
            health_percentage = max(0, 100 - (total_issues * 2))
            
            status = "CRITICAL" if health['errors'] > 10 else "WARNING" if health['errors'] > 5 else "OK"
            
            health_report += f"\n{component.upper()}:\n"
            health_report += f"  Status: {status}\n"
            health_report += f"  Health: {health_percentage:.1f}%\n"
            health_report += f"  Errors: {health['errors']}\n"
            health_report += f"  Warnings: {health['warnings']}\n"
            if health['last_issue']:
                health_report += f"  Last Issue: {health['last_issue']}\n"
        
        # System stability analysis
        health_report += "\n\nSystem Stability Analysis:\n"
        health_report += "-" * 40 + "\n"
        
        if system_uptime_issues:
            health_report += f"Critical System Events: {len(system_uptime_issues)}\n"
            for issue in system_uptime_issues[-5:]:  # Last 5 critical events
                health_report += f"  {issue['timestamp']}: {issue['message'][:100]}...\n"
        else:
            health_report += "No critical system downtime events detected.\n"
        
        # Recommendations
        health_report += "\n\nRecommendations:\n"
        health_report += "-" * 40 + "\n"
        
        if health_score < 70:
            health_report += "• URGENT: System requires immediate attention\n"
            health_report += "• Review critical errors and implement fixes\n"
            health_report += "• Consider preventive maintenance\n"
        elif health_score < 85:
            health_report += "• System performance is degraded\n"
            health_report += "• Address recurring warnings\n"
            health_report += "• Monitor system closely\n"
        else:
            health_report += "• System is operating within normal parameters\n"
            health_report += "• Continue regular monitoring\n"
        
        # Top problematic components
        top_problems = sorted(component_health.items(), 
                            key=lambda x: x[1]['errors'], reverse=True)[:5]
        
        if top_problems and top_problems[0][1]['errors'] > 0:
            health_report += "\nTop Problematic Components:\n"
            for i, (component, health) in enumerate(top_problems, 1):
                if health['errors'] > 0:
                    health_report += f"{i}. {component}: {health['errors']} errors\n"
        
        self.health_text.delete(1.0, tk.END)
        self.health_text.insert(tk.END, health_report)
        
        self.status_var.set("System health analysis complete")
        
    def apply_filter(self):
        """Apply filters to detailed log view"""
        if not self.log_data:
            return
            
        log_type = self.log_type_var.get()
        search_term = self.search_var.get().lower()
        
        filtered_content = f"FILTERED LOG VIEW - {log_type}\n"
        filtered_content += "=" * 80 + "\n\n"
        
        if log_type in self.log_data:
            entries = self.log_data[log_type]
            
            if search_term:
                entries = [entry for entry in entries if search_term in entry['message'].lower()]
            
            # Sort by timestamp
            entries.sort(key=lambda x: x['timestamp'])
            
            for entry in entries[-1000:]:  # Show last 1000 entries
                filtered_content += f"{entry['timestamp']} [{entry['category']}] [{entry['severity']}]\n"
                filtered_content += f"File: {entry['file']}:{entry['line']}\n"
                filtered_content += f"Message: {entry['message']}\n"
                filtered_content += "-" * 80 + "\n"
        
        self.detailed_text.delete(1.0, tk.END)
        self.detailed_text.insert(tk.END, filtered_content)
        
        # Switch to detailed view tab
        self.notebook.select(3)
        
    def generate_report(self):
        """Generate comprehensive analysis report"""
        if not self.log_data:
            messagebox.showwarning("Warning", "Please load log files first")
            return
            
        # Ask for save location
        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
            title="Save Analysis Report"
        )
        
        if not filename:
            return
            
        self.status_var.set("Generating comprehensive report...")
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("INDUSTRIAL SYSTEM LOG ANALYSIS REPORT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Analysis Period: {self.start_date.get()} to {self.end_date.get()}\n")
                f.write("=" * 80 + "\n\n")
                
                # Overview section
                f.write(self.stats_text.get(1.0, tk.END))
                f.write("\n" + "=" * 80 + "\n\n")
                
                # Run analyses and write to file
                self.error_correlation()
                f.write(self.correlation_text.get(1.0, tk.END))
                f.write("\n" + "=" * 80 + "\n\n")
                
                self.system_health_analysis()
                f.write(self.health_text.get(1.0, tk.END))
                
            messagebox.showinfo("Success", f"Report saved to {filename}")
            self.status_var.set("Report generated successfully")
            
        except Exception as e:
            messagebox.showerror("Error", f"Error generating report: {str(e)}")
            self.status_var.set("Error generating report")

def main():
    """Main application entry point"""
    root = tk.Tk()
    app = LogAnalyzer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
