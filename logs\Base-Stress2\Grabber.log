2020-11-05 09:42:42.2679|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Cameras found: 1 
2020-11-05 09:42:42.2679|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GevInterfaceSubnetIPAddress/GevInterfaceSubnetMask: ***********/*********** 
2020-11-05 09:42:42.2679|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f5 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500926 at **************/*********** on ***********/*********** subnet match 
2020-11-05 09:42:42.2679|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f5 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500926 accessible over this nic 
2020-11-05 09:42:42.2839|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Baumer Optronic MXGC40 700005500926  ok. 
2020-11-05 09:42:42.2839|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 09:42:42.2839|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Discovery: 1 cameras found. 
2020-11-05 09:42:42.2839|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|FoundCamerasRequestResponder: 
2020-11-05 09:42:42.2839|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Baumer Optronic MXGC40 700005500926 GEV RW  ippAxsBoth False: camera used True True True  
2020-11-05 09:42:42.2839|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=opening cameras 
2020-11-05 09:42:42.5770|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device DeviceID:             00_06_be_0f_de_f5 
2020-11-05 09:42:42.5770|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Model:                MXGC40 
2020-11-05 09:42:42.5770|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Vendor:               Baumer Optronic 
2020-11-05 09:42:42.5770|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device TLType:               GEV 
2020-11-05 09:42:42.5770|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device AccessStatus:         RW 
2020-11-05 09:42:42.5770|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device UserID:               MXGC40 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceID (SN):               700005500926 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceManufacturerInfo:      F:0100375C/C:0100613C/BL3.5:01000013 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceFirmwareVersion:       CID:010007/PID:11094944 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCCP:                      ControlAccessSwitchoverActive 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentIPAddress:         ************** 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentSubnetMask:        *********** 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Node Count:                  16 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=initializing cameras 
2020-11-05 09:42:42.5829|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|
0 name                = 
0 WarpEnable          = True 1 0 0 0
0 SwapImageTopBotAtY  = 0
0 MirrorAxsHorizontal = True
0 MirrorAxsVertical   = True
0 RescaleEnable       = False
0 RescaleFactor       = 1
0 SubtractGreyvalue   = 0
 
2020-11-05 09:42:42.6001|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCDA:                     0.0.0.0 
2020-11-05 09:42:42.6001|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorWidth = 2040 [0..65535/1] 
2020-11-05 09:42:42.6001|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorHeight = 2044 [0..65535/1] 
2020-11-05 09:42:42.6001|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningHorizontal = 1] 
2020-11-05 09:42:42.6001|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 09:42:42.6001|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningVertical = 1] 
2020-11-05 09:42:42.6189|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 09:42:42.6189|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [PixelFormat = Mono8] 
2020-11-05 09:42:42.6189|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 PixelFormat = Mono8 [Mono12/Mono12Packed/Mono8] 
2020-11-05 09:42:42.6189|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..0/2] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Width = 2040] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Width = 2040 [4..2040/4] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Height = 2040] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Height = 2040 [2..2044/2] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 09:42:42.6313|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 09:42:42.6430|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 09:42:42.6430|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 09:42:42.6430|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [HqMode = Off] 
2020-11-05 09:42:42.6430|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 09:42:42.6430|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 09:42:42.6430|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevHeartbeatTimeout = 10000] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevHeartbeatTimeout = 10000 [500..4294967295/1] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPSPacketSize = 9000] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPSPacketSize = 9000 [576..16110/2] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PacketDelay = 10000 bytes => for HXG/MXG/VLG..: 80000 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPD = 80000] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPD = 80000 [0..4294967295/1] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCFTD (FrameTransmissionDelay) 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCFTD = 0] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCFTD = 0 [0..4294967295/1] 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevTimestampTickFrequency=1000000000 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TimeStampFrequency64: 1000000000 
2020-11-05 09:42:42.6774|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime min..max = 20..1000000/0 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Shutter range: [20;1000000], current=4000 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 ExposureTime = 4000 [20..1000000/0] 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain min..max/ = 1..8/0 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain range: [1;8], current=1 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Gain = 1 [1..8/0] 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceTemperatureExceeded not available. 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PayloadSize = 4161600 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Current:  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Reset  :  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineSelector = Line0] 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineSelector = Line0 [Line0/Line1/Line2/Line3] 
2020-11-05 09:42:42.6905|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineSelector=Line0 
2020-11-05 09:42:42.7053|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerHighTimeAbs = 5] 
2020-11-05 09:42:42.7053|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerHighTimeAbs = 5 [0..5000/0] 
2020-11-05 09:42:42.7053|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerHighTimeAbs=5 
2020-11-05 09:42:42.7053|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerLowTimeAbs = 5] 
2020-11-05 09:42:42.7053|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerLowTimeAbs = 5 [0..5000/0] 
2020-11-05 09:42:42.7053|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerLowTimeAbs=5 
2020-11-05 09:42:42.7053|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Datastreams:                 1 
2020-11-05 09:42:42.7475|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Open datastream:             Stream0 
2020-11-05 09:42:42.7475|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamAnnounceBufferMinimum: 2 
2020-11-05 09:42:42.7547|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 09:42:42.7547|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Filter 
2020-11-05 09:42:42.7547|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Socket 
2020-11-05 09:42:42.7547|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 09:42:42.7547|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Allocated buffers:           20 
2020-11-05 09:42:42.7547|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 0 16A6E9F5040 
2020-11-05 09:42:42.7547|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 1 16A6F1F2040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 2 16A6F9F2040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 3 16A701FE040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 4 16A70A0C040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 5 16A71218040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 6 16A71A1E040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 7 16A7222A040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 8 16A72A29040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 9 16A73221040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 10 16A73A2B040 
2020-11-05 09:42:42.7680|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 11 16A74234040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 12 16A74A36040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 13 16A75238040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 14 16A75A33040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 15 16A76230040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 16 16A76A34040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 17 16A77235040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 18 16A77A30040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 19 16A78234040 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Queued buffers:              20 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource 'All':         supported 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource:               All 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerMode:                 On 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime:                500 
2020-11-05 09:42:42.7843|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Register Event Mode to:      EVENT_HANDLER 
2020-11-05 09:42:42.8101|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 AcquisitionStart 
2020-11-05 09:42:42.8101|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=ok. 
2020-11-05 09:42:42.8101|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetShutter=250 
2020-11-05 09:42:42.8143|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                250 
2020-11-05 09:42:42.8143|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetGain=1 
2020-11-05 09:42:42.8143|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:42:42.8336|Process 1088|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Enable triggers. 
2020-11-05 14:15:15.5004|Process 6456|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|BaumerPictureSource2 created 
2020-11-05 14:15:15.5121|Process 6456|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|status=reading configuration 
2020-11-05 14:15:15.5462|Process 6456|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 StackPictures=False 
2020-11-05 14:15:15.5462|Process 6456|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Config: SN=700005500926, CamRect={X=0,Y=0,Width=2040,Height=2040}, PicSizeOut={Width=1020, Height=1020} 
2020-11-05 14:15:15.5462|Process 6456|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Camera0 warp => DefaultZoom=1 AngleDeg=0 OffsetX=0 OffsetY=0 
2020-11-05 14:15:15.6220|Process 6456|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartAcquisition|status=StartAcquisition 
2020-11-05 14:15:15.6407|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.CheckCameras|------------ 
2020-11-05 14:15:16.8481|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|StartCameraSubSystem 1 
2020-11-05 14:15:16.8668|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Disable triggers. 
2020-11-05 14:15:16.8668|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching systems 0 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected systems:  1 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System GEV found: C:\Symplex.NET\bgapi2_gige.cti_2.7.14708.14730 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|mSystem.Open() 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:15:16.9035|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:15:16.9191|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GenTL Version:   1.5 
2020-11-05 14:15:16.9191|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=assigning interfaces 1 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected interfaces: 4 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {907ADD6B-2066-4BA6-BFB2-E4DAFD42CAAB} 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #2 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: 0.0.0.0/0.0.0.0 <- do not include 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4121C4C2-2085-4A6D-837F-FB555CAFED42} 
2020-11-05 14:15:16.9284|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ************/************* <- do not include 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4E4D9349-6426-4FD0-AC75-AC35B548AE0D} 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #3 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- include 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  use interface 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {8C712529-904F-4E1D-AC54-AFE171402265} 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) Ethernet Connection (7) I219-LM 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- do not include 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching cameras 1 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Checking interface:  
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4E4D9349-6426-4FD0-AC75-AC35B548AE0D} 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.9396|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #3 
2020-11-05 14:15:17.6265|Process 6456|GrabberProcess|Thread 14|Base-Stress2-Grabber receive thread|ERROR|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Invalid gain range, cannot set gain. 
2020-11-05 14:15:24.9978|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Cameras found: 1 
2020-11-05 14:15:24.9978|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GevInterfaceSubnetIPAddress/GevInterfaceSubnetMask: ***********/*********** 
2020-11-05 14:15:24.9978|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f5 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500926 at **************/*********** on ***********/*********** subnet match 
2020-11-05 14:15:24.9978|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f5 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500926 accessible over this nic 
2020-11-05 14:15:24.9978|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Baumer Optronic MXGC40 700005500926  ok. 
2020-11-05 14:15:24.9978|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:15:25.0136|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Discovery: 1 cameras found. 
2020-11-05 14:15:25.0136|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|FoundCamerasRequestResponder: 
2020-11-05 14:15:25.0136|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Baumer Optronic MXGC40 700005500926 GEV RW  ippAxsBoth False: camera used True True True  
2020-11-05 14:15:25.0136|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=opening cameras 
2020-11-05 14:15:25.2799|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device DeviceID:             00_06_be_0f_de_f5 
2020-11-05 14:15:25.2799|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Model:                MXGC40 
2020-11-05 14:15:25.2799|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Vendor:               Baumer Optronic 
2020-11-05 14:15:25.2799|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device TLType:               GEV 
2020-11-05 14:15:25.2799|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device AccessStatus:         RW 
2020-11-05 14:15:25.2799|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device UserID:               MXGC40 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceID (SN):               700005500926 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceManufacturerInfo:      F:0100375C/C:0100613C/BL3.5:01000013 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceFirmwareVersion:       CID:010007/PID:11094944 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCCP:                      ControlAccessSwitchoverActive 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentIPAddress:         ************** 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentSubnetMask:        *********** 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Node Count:                  16 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=initializing cameras 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|
0 name                = 
0 WarpEnable          = True 1 0 0 0
0 SwapImageTopBotAtY  = 0
0 MirrorAxsHorizontal = True
0 MirrorAxsVertical   = True
0 RescaleEnable       = False
0 RescaleFactor       = 1
0 SubtractGreyvalue   = 0
 
2020-11-05 14:15:25.2860|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCDA:                     *********** 
2020-11-05 14:15:25.3055|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorWidth = 2040 [0..65535/1] 
2020-11-05 14:15:25.3055|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorHeight = 2044 [0..65535/1] 
2020-11-05 14:15:25.3055|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningHorizontal = 1] 
2020-11-05 14:15:25.3055|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:15:25.3055|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningVertical = 1] 
2020-11-05 14:15:25.3211|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:15:25.3211|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [PixelFormat = Mono8] 
2020-11-05 14:15:25.3211|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 PixelFormat = Mono8 [Mono12/Mono12Packed/Mono8] 
2020-11-05 14:15:25.3211|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:15:25.3211|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:15:25.3211|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Width = 2040] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Width = 2040 [4..2040/4] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Height = 2040] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Height = 2040 [2..2044/2] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:15:25.3387|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:15:25.3466|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 14:15:25.3466|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [HqMode = Off] 
2020-11-05 14:15:25.3466|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:15:25.3466|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:15:25.3466|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevHeartbeatTimeout = 10000] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevHeartbeatTimeout = 10000 [500..4294967295/1] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPSPacketSize = 9000] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPSPacketSize = 9000 [576..16110/2] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PacketDelay = 10000 bytes => for HXG/MXG/VLG..: 80000 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPD = 80000] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPD = 80000 [0..4294967295/1] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCFTD (FrameTransmissionDelay) 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCFTD = 0] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCFTD = 0 [0..4294967295/1] 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevTimestampTickFrequency=1000000000 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TimeStampFrequency64: 1000000000 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime min..max = 20..1000000/0 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Shutter range: [20;1000000], current=250 
2020-11-05 14:15:25.3792|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 ExposureTime = 250 [20..1000000/0] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain min..max/ = 1..8/0 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain range: [1;8], current=1 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Gain = 1 [1..8/0] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceTemperatureExceeded not available. 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PayloadSize = 4161600 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Current:  (HW) FrameCounter = 1535 cam.FrameCountSw = 0 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Reset  :  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineSelector = Line0] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineSelector = Line0 [Line0/Line1/Line2/Line3] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineSelector=Line0 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerHighTimeAbs = 5] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerHighTimeAbs = 5 [0..5000/0] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerHighTimeAbs=5 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerLowTimeAbs = 5] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerLowTimeAbs = 5 [0..5000/0] 
2020-11-05 14:15:25.3947|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerLowTimeAbs=5 
2020-11-05 14:15:25.4115|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Datastreams:                 1 
2020-11-05 14:15:25.4372|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Open datastream:             Stream0 
2020-11-05 14:15:25.4372|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamAnnounceBufferMinimum: 2 
2020-11-05 14:15:25.4372|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:15:25.4372|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Filter 
2020-11-05 14:15:25.4372|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Socket 
2020-11-05 14:15:25.4372|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Allocated buffers:           20 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 0 2160000E040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 1 21600813040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 2 21601018040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 3 2160181A040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 4 21602010040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 5 2160281A040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 6 21603019040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 7 2160381D040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 8 21604026040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 9 21604824040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 10 2160502D040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 11 2160583C040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 12 21606040040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 13 2160684B040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 14 21607051040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 15 21607854040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 16 21608052040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 17 21608851040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 18 21609053040 
2020-11-05 14:15:25.4529|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 19 21609857040 
2020-11-05 14:15:25.4688|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Queued buffers:              20 
2020-11-05 14:15:25.4688|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource 'All':         supported 
2020-11-05 14:15:25.4688|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource:               All 
2020-11-05 14:15:25.4688|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerMode:                 On 
2020-11-05 14:15:25.4688|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime:                500 
2020-11-05 14:15:25.4688|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Register Event Mode to:      EVENT_HANDLER 
2020-11-05 14:15:25.4925|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 AcquisitionStart 
2020-11-05 14:15:25.4925|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=ok. 
2020-11-05 14:15:25.4925|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetShutter=250 
2020-11-05 14:15:25.4925|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                250 
2020-11-05 14:15:25.4925|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetGain=1 
2020-11-05 14:15:25.4925|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 14:15:25.5083|Process 6456|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Enable triggers. 
2020-11-05 14:19:32.1383|Process 6340|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|BaumerPictureSource2 created 
2020-11-05 14:19:32.1576|Process 6340|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|status=reading configuration 
2020-11-05 14:19:32.1732|Process 6340|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 StackPictures=False 
2020-11-05 14:19:32.1732|Process 6340|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Config: SN=700005500926, CamRect={X=0,Y=0,Width=2040,Height=2040}, PicSizeOut={Width=1020, Height=1020} 
2020-11-05 14:19:32.1732|Process 6340|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Camera0 warp => DefaultZoom=1 AngleDeg=0 OffsetX=0 OffsetY=0 
2020-11-05 14:19:32.2290|Process 6340|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartAcquisition|status=StartAcquisition 
2020-11-05 14:19:32.2603|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.CheckCameras|------------ 
2020-11-05 14:19:33.3220|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|StartCameraSubSystem 1 
2020-11-05 14:19:33.4332|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Disable triggers. 
2020-11-05 14:19:33.4332|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching systems 0 
2020-11-05 14:19:33.4498|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected systems:  1 
2020-11-05 14:19:33.4498|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:19:33.4498|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:19:33.4498|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:19:33.4498|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System GEV found: C:\Symplex.NET\bgapi2_gige.cti_2.7.14708.14730 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|mSystem.Open() 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GenTL Version:   1.5 
2020-11-05 14:19:33.4607|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=assigning interfaces 1 
2020-11-05 14:19:33.4758|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected interfaces: 4 
2020-11-05 14:19:33.4758|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {907ADD6B-2066-4BA6-BFB2-E4DAFD42CAAB} 
2020-11-05 14:19:33.4758|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.4758|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #2 
2020-11-05 14:19:33.4971|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: 0.0.0.0/0.0.0.0 <- do not include 
2020-11-05 14:19:33.4971|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:19:33.4971|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4121C4C2-2085-4A6D-837F-FB555CAFED42} 
2020-11-05 14:19:33.4971|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.4971|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection 
2020-11-05 14:19:33.4971|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ************/************* <- do not include 
2020-11-05 14:19:33.5106|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:19:33.5106|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4E4D9349-6426-4FD0-AC75-AC35B548AE0D} 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #3 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- include 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  use interface 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {8C712529-904F-4E1D-AC54-AFE171402265} 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) Ethernet Connection (7) I219-LM 
2020-11-05 14:19:33.5400|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- do not include 
2020-11-05 14:19:33.5560|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:19:33.5560|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching cameras 1 
2020-11-05 14:19:33.5560|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Checking interface:  
2020-11-05 14:19:33.5560|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4E4D9349-6426-4FD0-AC75-AC35B548AE0D} 
2020-11-05 14:19:33.5712|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.5916|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #3 
2020-11-05 14:19:34.5499|Process 6340|GrabberProcess|Thread 16|Base-Stress2-Grabber receive thread|ERROR|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Invalid gain range, cannot set gain. 
2020-11-05 14:19:41.6519|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Cameras found: 1 
2020-11-05 14:19:41.6519|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GevInterfaceSubnetIPAddress/GevInterfaceSubnetMask: ***********/*********** 
2020-11-05 14:19:41.6705|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f5 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500926 at **************/*********** on ***********/*********** subnet match 
2020-11-05 14:19:41.6705|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f5 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500926 accessible over this nic 
2020-11-05 14:19:41.6705|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Baumer Optronic MXGC40 700005500926  ok. 
2020-11-05 14:19:41.6776|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:19:41.6776|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Discovery: 1 cameras found. 
2020-11-05 14:19:41.6776|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|FoundCamerasRequestResponder: 
2020-11-05 14:19:41.6776|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Baumer Optronic MXGC40 700005500926 GEV RW  ippAxsBoth False: camera used True True True  
2020-11-05 14:19:41.6776|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=opening cameras 
2020-11-05 14:19:41.9663|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device DeviceID:             00_06_be_0f_de_f5 
2020-11-05 14:19:41.9663|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Model:                MXGC40 
2020-11-05 14:19:41.9663|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Vendor:               Baumer Optronic 
2020-11-05 14:19:41.9663|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device TLType:               GEV 
2020-11-05 14:19:41.9663|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device AccessStatus:         RW 
2020-11-05 14:19:41.9663|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device UserID:               MXGC40 
2020-11-05 14:19:41.9663|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceID (SN):               700005500926 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceManufacturerInfo:      F:0100375C/C:0100613C/BL3.5:01000013 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceFirmwareVersion:       CID:010007/PID:11094944 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCCP:                      ControlAccessSwitchoverActive 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentIPAddress:         ************** 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentSubnetMask:        *********** 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Node Count:                  16 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=initializing cameras 
2020-11-05 14:19:41.9735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|
0 name                = 
0 WarpEnable          = True 1 0 0 0
0 SwapImageTopBotAtY  = 0
0 MirrorAxsHorizontal = True
0 MirrorAxsVertical   = True
0 RescaleEnable       = False
0 RescaleFactor       = 1
0 SubtractGreyvalue   = 0
 
2020-11-05 14:19:41.9883|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCDA:                     0.0.0.0 
2020-11-05 14:19:41.9883|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorWidth = 2040 [0..65535/1] 
2020-11-05 14:19:41.9883|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorHeight = 2044 [0..65535/1] 
2020-11-05 14:19:41.9883|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningHorizontal = 1] 
2020-11-05 14:19:42.0052|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:19:42.0052|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningVertical = 1] 
2020-11-05 14:19:42.0052|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:19:42.0052|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [PixelFormat = Mono8] 
2020-11-05 14:19:42.0241|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 PixelFormat = Mono8 [Mono12/Mono12Packed/Mono8] 
2020-11-05 14:19:42.0241|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:19:42.0241|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:19:42.0241|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:19:42.0241|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..0/2] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Width = 2040] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Width = 2040 [4..2040/4] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Height = 2040] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Height = 2040 [2..2044/2] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [HqMode = Off] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:19:42.0347|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevHeartbeatTimeout = 10000] 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevHeartbeatTimeout = 10000 [500..4294967295/1] 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPSPacketSize = 9000] 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPSPacketSize = 9000 [576..16110/2] 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PacketDelay = 10000 bytes => for HXG/MXG/VLG..: 80000 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPD = 80000] 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPD = 80000 [0..4294967295/1] 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCFTD (FrameTransmissionDelay) 
2020-11-05 14:19:42.0735|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCFTD = 0] 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCFTD = 0 [0..4294967295/1] 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevTimestampTickFrequency=1000000000 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TimeStampFrequency64: 1000000000 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime min..max = 20..1000000/0 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Shutter range: [20;1000000], current=4000 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 ExposureTime = 4000 [20..1000000/0] 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain min..max/ = 1..8/0 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain range: [1;8], current=1 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Gain = 1 [1..8/0] 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceTemperatureExceeded not available. 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PayloadSize = 4161600 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Current:  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Reset  :  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineSelector = Line0] 
2020-11-05 14:19:42.0814|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineSelector = Line0 [Line0/Line1/Line2/Line3] 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineSelector=Line0 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerHighTimeAbs = 5] 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerHighTimeAbs = 5 [0..5000/0] 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerHighTimeAbs=5 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerLowTimeAbs = 5] 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerLowTimeAbs = 5 [0..5000/0] 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerLowTimeAbs=5 
2020-11-05 14:19:42.0994|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Datastreams:                 1 
2020-11-05 14:19:42.1405|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Open datastream:             Stream0 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamAnnounceBufferMinimum: 2 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Filter 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Socket 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Allocated buffers:           20 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 0 180BD6ED040 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 1 180BDEF6040 
2020-11-05 14:19:42.1451|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 2 180BE6F1040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 3 180BEEFF040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 4 180BF70E040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 5 180BFF1A040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 6 180C071E040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 7 180C0F2D040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 8 180C1737040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 9 180C1F34040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 10 180C2738040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 11 180C2F39040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 12 180C373F040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 13 180C3F4B040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 14 180C4758040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 15 180C4F5B040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 16 180C5766040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 17 180C5F65040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 18 180C6768040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 19 180C6F6A040 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Queued buffers:              20 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource 'All':         supported 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource:               All 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerMode:                 On 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime:                500 
2020-11-05 14:19:42.1597|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Register Event Mode to:      EVENT_HANDLER 
2020-11-05 14:19:42.1861|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 AcquisitionStart 
2020-11-05 14:19:42.1861|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=ok. 
2020-11-05 14:19:42.1861|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetShutter=250 
2020-11-05 14:19:42.1923|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                250 
2020-11-05 14:19:42.1923|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetGain=1 
2020-11-05 14:19:42.1923|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 14:19:42.2120|Process 6340|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Enable triggers. 
