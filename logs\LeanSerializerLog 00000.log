2023-08-21 09:08:13.2197  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 09:08:42.2202  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 09:08:42.4749  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 09:44:39.3796  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-22 10:06:36.9176  Health Monitoring shutdown
2023-08-22 10:06:36.9116  ParameterUI application shut down
2023-08-22 10:06:37.1386  Main application shut down
2023-08-22 10:06:37.9548  Health Monitoring started
2023-08-22 10:06:40.0171  ParameterUI application started
2023-08-22 10:06:40.9481  Main application started. Version: R2021.1.9.573
2023-08-22 10:06:42.1692  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-22 10:06:54.0836  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-22 10:06:54.0836  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-22 10:15:24.1841  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-22 10:15:24.3742  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-22 10:15:24.4552  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-22 11:06:56.0519  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-23 11:13:27.6617  Health Monitoring shutdown
2023-08-23 11:13:27.7187  ParameterUI application shut down
2023-08-23 11:13:27.8647  Main application shut down
2023-08-23 11:13:28.6521  Health Monitoring started
2023-08-23 11:13:30.7875  ParameterUI application started
2023-08-23 11:13:31.6916  Main application started. Version: R2021.1.9.573
2023-08-23 11:13:32.9149  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-23 11:13:45.3367  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-23 11:13:45.3367  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-23 11:45:54.2453  Health Monitoring started
2023-08-23 11:45:55.8963  Main application started. Version: R2021.1.9.573
2023-08-23 11:45:57.6253  ParameterUI application started
2023-08-23 11:45:58.7283  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-23 11:46:12.1514  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-23 11:46:12.1514  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-23 12:46:14.4640  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-24 08:26:08.9106  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-25 10:53:52.5628  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-25 10:53:52.6358  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-21 13:52:51.8322  Main application started. Version: R2021.1.9.573
2023-09-21 13:52:52.9552  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-21 13:53:05.1127  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-21 13:53:05.1127  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-21 13:58:51.1467  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-21 14:53:05.2770  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-28 21:57:12.9420  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-28 22:11:45.7462  ParameterUI application shut down
2023-09-28 22:14:02.2229  Health Monitoring started
2023-09-28 22:14:03.8339  Main application started. Version: R2021.1.9.573
2023-09-28 22:14:05.5669  ParameterUI application started
2023-09-28 22:14:06.7159  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-28 22:14:20.7391  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-28 22:14:20.7391  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-28 23:14:22.0772  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-29 02:38:00.5144  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-29 02:38:00.8114  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-09-29 02:38:00.9294  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-12 08:58:53.0788  Health Monitoring shutdown
2023-10-12 08:58:53.1018  ParameterUI application shut down
2023-10-12 08:58:53.3903  Main application shut down
2023-10-12 08:58:54.1496  Health Monitoring started
2023-10-12 08:58:55.6769  Main application started. Version: R2021.1.9.573
2023-10-12 08:58:56.8346  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-12 08:58:57.0237  ParameterUI application started
2023-10-12 08:59:07.9445  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-12 08:59:09.4100  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-12 08:59:09.4100  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-12 09:19:25.6296  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-12 09:19:25.8986  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-12 09:59:10.9887  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-16 16:45:25.3944  Main application started. Version: R2021.1.9.573
2023-10-16 16:45:26.6664  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-16 16:45:38.0164  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-16 16:45:38.0174  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-16 17:45:38.2297  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-17 10:32:53.3923  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 11:00:56.2895  Health Monitoring shutdown
2023-10-18 11:00:56.4695  Main application shut down
2023-10-18 11:00:57.3627  Health Monitoring started
2023-10-18 11:00:58.8476  Main application started. Version: R2021.1.9.573
2023-10-18 11:00:59.9410  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 11:01:11.1116  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 11:01:11.1116  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 11:03:56.6206  Health Monitoring started
2023-10-18 11:03:58.2116  Main application started. Version: R2021.1.9.573
2023-10-18 11:03:59.8436  ParameterUI application started
2023-10-18 11:04:00.9576  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 11:04:14.7274  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 11:04:14.7274  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 11:18:53.4328  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-18 12:04:15.4392  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-19 12:11:10.0195  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-19 12:11:10.2325  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-24 10:08:54.5713  ParameterUI application shut down
2023-10-24 10:08:54.6133  Health Monitoring shutdown
2023-10-24 10:08:54.8443  Main application shut down
2023-10-24 10:08:55.6680  Health Monitoring started
2023-10-24 10:08:57.1898  Main application started. Version: R2021.1.9.573
2023-10-24 10:08:58.3364  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-24 10:08:58.5784  ParameterUI application started
2023-10-24 10:09:10.7830  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-24 10:09:10.7850  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-24 11:09:11.3824  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-24 11:22:32.1487  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-24 11:22:32.4002  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-10-24 11:22:32.8892  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-12 20:21:36.6004  ParameterUI application started
2023-12-13 01:58:18.5951  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-14 03:10:34.4872  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 00:38:10.2151  ParameterUI application started
2023-12-22 00:38:13.2427  ParameterUI application started
2023-12-22 00:38:19.8546  ParameterUI application started
2023-12-22 00:38:23.2786  ParameterUI application started
2023-12-22 00:38:37.0965  ParameterUI application started
2023-12-22 00:38:50.1402  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 02:36:32.0973  ParameterUI application started
2023-12-22 02:36:49.3091  ParameterUI application started
2023-12-22 02:36:56.3311  ParameterUI application started
2023-12-22 02:37:28.5835  ParameterUI application started
2023-12-22 07:23:55.2082  Health Monitoring started
2023-12-22 07:23:56.7832  Main application started. Version: R2021.1.9.573
2023-12-22 07:23:58.6052  ParameterUI application started
2023-12-22 07:23:59.7302  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 07:24:13.6370  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 07:24:13.6370  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 07:28:35.1026  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 07:32:57.9359  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 07:41:27.9583  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-22 07:41:28.0463  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-24 20:34:16.4503  Health Monitoring started
2023-12-24 20:34:18.0233  Main application started. Version: R2021.1.9.573
2023-12-24 20:34:19.7903  ParameterUI application started
2023-12-24 20:34:20.9333  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-24 20:34:34.5178  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-24 20:34:34.5178  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-24 21:34:36.3002  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-26 13:02:59.3540  Main application started. Version: R2021.1.9.573
2023-12-26 13:03:00.5130  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-26 13:03:11.6835  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-26 13:03:11.6835  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-26 14:03:11.8996  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-28 07:02:09.0103  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-28 07:14:01.7165  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-12-28 07:14:01.7965  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-03 14:23:33.5795  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-05 01:59:15.3430  Main application started. Version: R2021.1.9.573
2024-01-05 01:59:16.4940  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-05 01:59:27.6947  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-05 01:59:27.6947  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-05 02:59:27.8972  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-05 07:57:28.0232  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-14 00:25:40.9437  ParameterUI application started
2024-01-14 00:25:52.9899  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-14 00:56:06.3035  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-20 16:21:15.2462  ParameterUI application shut down
2024-01-20 16:23:02.1734  Health Monitoring started
2024-01-20 16:23:03.7174  Main application started. Version: R2021.1.9.573
2024-01-20 16:23:05.5314  ParameterUI application started
2024-01-20 16:23:06.6504  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-20 16:23:21.0003  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-20 16:23:21.0003  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-20 16:24:47.0367  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-20 16:24:47.1647  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-20 16:24:47.2667  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-20 17:23:22.6055  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-25 07:34:34.4646  ParameterUI application shut down
2024-01-25 07:34:34.4646  Health Monitoring shutdown
2024-01-25 07:34:34.6646  Main application shut down
2024-01-25 07:34:35.4789  Health Monitoring started
2024-01-25 07:34:37.5691  ParameterUI application started
2024-01-25 07:34:38.5181  Main application started. Version: R2021.1.9.573
2024-01-25 07:34:39.7774  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-25 07:34:52.2195  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-25 07:34:52.2195  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-25 08:34:53.6907  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-25 10:42:49.0558  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-25 10:42:49.2798  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-01-25 10:42:49.3768  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 08:52:24.5297  Health Monitoring shutdown
2024-02-24 08:52:24.5927  ParameterUI application shut down
2024-02-24 08:52:25.0955  Main application shut down
2024-02-24 08:52:25.6617  Health Monitoring started
2024-02-24 08:52:27.7406  ParameterUI application started
2024-02-24 08:52:28.7003  Main application started. Version: R2021.1.9.573
2024-02-24 08:52:29.9895  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 08:52:42.2460  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 08:52:42.2460  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 08:57:19.3305  Health Monitoring started
2024-02-24 08:57:20.8945  Main application started. Version: R2021.1.9.573
2024-02-24 08:57:22.6855  ParameterUI application started
2024-02-24 08:57:23.7385  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 08:57:38.3525  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 08:57:38.3535  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 09:34:18.6583  ParameterUI application shut down
2024-02-24 09:34:18.7123  Health Monitoring shutdown
2024-02-24 09:34:18.7633  Main application shut down
2024-02-24 09:34:19.6840  Health Monitoring started
2024-02-24 09:34:21.6816  ParameterUI application started
2024-02-24 09:34:22.5411  Main application started. Version: R2021.1.9.573
2024-02-24 09:34:23.8520  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 09:34:36.0679  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 09:34:36.0679  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 09:36:40.2588  Health Monitoring started
2024-02-24 09:36:41.7908  Main application started. Version: R2021.1.9.573
2024-02-24 09:36:43.6048  ParameterUI application started
2024-02-24 09:36:44.6898  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 09:36:59.1269  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 09:36:59.1269  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-24 10:37:00.6069  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-25 07:08:52.5020  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-25 07:08:52.6540  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-25 07:08:52.7600  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-29 12:39:30.2621  ParameterUI application started
2024-02-29 12:39:44.0352  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-02-29 12:55:23.8988  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-10 05:10:41.3110  Main application started. Version: R2021.1.9.573
2024-03-10 05:10:42.5170  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-10 05:10:54.7830  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-10 05:10:54.7830  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-10 06:10:55.0466  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-12 08:18:09.4041  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-16 06:27:46.1768  Main application started. Version: R2021.1.9.573
2024-03-16 06:27:47.3948  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-16 06:27:58.4756  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-16 06:27:58.4756  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-16 07:27:58.6522  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-18 07:15:30.1104  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-18 11:52:52.3750  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-03-27 14:04:13.0733  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-14 12:12:16.0551  Main application started. Version: R2021.1.9.573
2024-04-14 12:12:17.1501  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-14 12:12:28.5563  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-14 12:12:28.5563  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-14 12:12:43.9680  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-14 13:12:28.7251  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-15 07:09:53.3154  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-18 22:34:17.4349  Main application started. Version: R2021.1.9.573
2024-04-18 22:34:18.6329  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-18 22:34:30.0257  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-18 22:34:30.0257  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-18 22:34:46.0586  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-18 23:34:30.1907  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-04-20 15:30:20.3445  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 08:50:12.6024  Health Monitoring started
2024-05-14 08:50:14.1384  Main application started. Version: R2021.1.9.573
2024-05-14 08:50:16.6404  ParameterUI application started
2024-05-14 08:50:19.1614  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 08:50:29.3518  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 08:50:32.8532  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 08:50:32.8532  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 08:50:49.2391  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 09:21:54.5428  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 09:21:54.6178  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-05-14 09:50:34.0338  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:49:54.0513  Health Monitoring shutdown
2024-06-03 09:49:54.0723  ParameterUI application shut down
2024-06-03 09:49:54.5534  Main application shut down
2024-06-03 09:49:55.0530  Health Monitoring started
2024-06-03 09:49:57.5523  ParameterUI application started
2024-06-03 09:49:58.4529  Main application started. Version: R2021.1.9.573
2024-06-03 09:49:59.7362  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:50:12.1002  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:50:12.1002  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:50:24.1655  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:51:00.9988  ParameterUI application shut down
2024-06-03 09:53:28.8231  Health Monitoring started
2024-06-03 09:53:30.3661  Main application started. Version: R2021.1.9.573
2024-06-03 09:53:32.1001  ParameterUI application started
2024-06-03 09:53:33.5411  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:53:48.2710  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:53:48.2710  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 09:53:52.4688  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 10:28:01.9336  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 10:53:49.7779  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 11:55:32.3265  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-03 11:55:32.3925  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:00:31.6734  ParameterUI application shut down
2024-06-10 11:00:31.6744  Health Monitoring shutdown
2024-06-10 11:00:31.8834  Main application shut down
2024-06-10 11:00:32.6780  Health Monitoring started
2024-06-10 11:00:34.2028  Main application started. Version: R2021.1.9.573
2024-06-10 11:00:35.3472  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:00:35.4742  ParameterUI application started
2024-06-10 11:00:47.4570  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:00:47.4570  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:01:01.6908  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:03:24.2432  Health Monitoring started
2024-06-10 11:03:25.8462  Main application started. Version: R2021.1.9.573
2024-06-10 11:03:27.5862  ParameterUI application started
2024-06-10 11:03:29.1952  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:03:42.4694  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:03:42.4694  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:03:47.3734  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:14:26.2833  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:14:26.6443  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 11:14:26.7583  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-06-10 12:03:42.9811  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-20 22:01:11.0679  Main application started. Version: R2021.1.9.573
2024-07-20 22:01:12.2989  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-20 22:01:23.5079  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-20 22:01:23.5099  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-20 22:01:38.3865  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-20 23:01:23.7018  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-21 23:45:38.7992  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-30 09:28:58.4889  ParameterUI application started
2024-07-30 09:34:49.2794  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-07-30 09:34:49.5694  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 07:05:16.4885  Health Monitoring started
2024-08-28 07:05:18.0244  Main application started. Version: R2021.1.9.573
2024-08-28 07:05:19.8214  ParameterUI application started
2024-08-28 07:05:21.4694  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 07:05:34.5007  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 07:05:34.5007  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 07:05:56.3681  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 07:09:59.8512  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 07:16:07.4584  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 07:16:07.5064  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-08-28 08:05:34.9849  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 06:58:57.0130  ParameterUI application shut down
2024-09-02 07:28:25.6820  Health Monitoring started
2024-09-02 07:28:27.2480  Main application started. Version: R2021.1.9.573
2024-09-02 07:28:29.0790  ParameterUI application started
2024-09-02 07:28:30.7390  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 07:28:45.4689  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 07:28:45.4689  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 07:28:54.6787  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 07:51:39.9029  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 07:55:17.9434  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 07:55:18.0664  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 08:28:45.8436  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 12:05:02.7756  ParameterUI application shut down
2024-09-02 12:05:02.7586  Health Monitoring shutdown
2024-09-02 12:05:02.8976  Main application shut down
2024-09-02 12:05:03.8376  Health Monitoring started
2024-09-02 12:05:05.3944  Main application started. Version: R2021.1.9.573
2024-09-02 12:05:06.5514  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 12:05:06.7822  ParameterUI application started
2024-09-02 12:05:18.9967  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 12:05:18.9967  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 12:05:32.9091  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 12:05:53.0460  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 12:05:53.1820  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 12:05:53.2650  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-02 13:05:20.1697  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-03 05:44:19.1896  Main application started. Version: R2021.1.9.573
2024-09-03 05:44:20.3426  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-03 05:44:32.6119  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-03 05:44:32.6129  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-03 05:44:47.9514  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-03 06:44:32.8149  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-04 15:06:40.8183  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 11:14:46.5889  ParameterUI application started
2024-09-19 11:14:49.7889  ParameterUI application started
2024-09-19 11:14:55.4279  ParameterUI application started
2024-09-19 11:14:58.6789  ParameterUI application started
2024-09-19 11:15:11.3207  ParameterUI application started
2024-09-19 11:15:37.4171  ParameterUI application started
2024-09-19 11:15:43.4020  ParameterUI application started
2024-09-19 11:16:13.5139  ParameterUI application started
2024-09-19 11:16:40.7239  ParameterUI application started
2024-09-19 11:16:43.9805  ParameterUI application started
2024-09-19 11:16:54.6106  ParameterUI application started
2024-09-19 11:17:06.2447  ParameterUI application started
2024-09-19 11:17:09.5084  ParameterUI application started
2024-09-19 11:17:54.2914  ParameterUI application started
2024-09-19 11:18:04.5740  ParameterUI application started
2024-09-19 11:18:38.4219  Health Monitoring shutdown
2024-09-19 11:18:37.7079  ParameterUI application started
2024-09-19 11:18:39.0216  Main application shut down
2024-09-19 11:21:34.8560  Health Monitoring started
2024-09-19 11:21:36.4339  Main application started. Version: R2021.1.9.573
2024-09-19 11:21:37.9739  ParameterUI application started
2024-09-19 11:21:39.3670  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 11:21:53.0859  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 11:21:53.0859  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 11:22:27.0430  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 11:23:59.5596  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 11:47:55.6868  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 11:47:55.8488  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-09-19 12:21:54.6114  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 08:17:45.2594  ParameterUI application shut down
2024-10-01 08:17:45.2564  Health Monitoring shutdown
2024-10-01 08:17:45.6074  Main application shut down
2024-10-01 08:17:46.4592  Health Monitoring started
2024-10-01 08:17:47.9723  Main application started. Version: R2021.1.9.573
2024-10-01 08:17:49.1203  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 08:17:49.3713  ParameterUI application started
2024-10-01 08:18:01.0004  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 08:18:01.0004  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 08:18:15.2729  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 08:35:46.3191  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 08:35:47.1531  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 08:35:47.2728  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-01 09:18:01.7754  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-27 17:41:13.3518  Main application started. Version: R2021.1.9.573
2024-10-27 17:41:14.5258  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-27 17:41:26.9575  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-27 17:41:26.9575  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-27 17:41:41.0407  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-27 18:41:27.3678  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-10-28 02:10:46.6833  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 06:16:40.9655  Main application started. Version: R2021.1.9.573
