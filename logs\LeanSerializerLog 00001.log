2021-10-29 11:10:38.4200  Main application started. Version: R2021.1.6.573
2021-10-29 11:10:39.6679  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-10-29 11:10:40.1876  ParameterUI application started
2021-10-29 11:10:52.6561  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-10-29 11:10:52.6561  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-10-29 12:10:52.9959  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 13:53:15.7050  ParameterUI application shut down
2021-11-02 13:53:15.7490  Health Monitoring shutdown
2021-11-02 13:53:15.8647  Main application shut down
2021-11-02 13:53:16.7268  Health Monitoring started
2021-11-02 13:53:18.2228  Main application started. Version: R2021.1.6.573
2021-11-02 13:53:19.2689  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 13:53:19.6798  ParameterUI application started
2021-11-02 13:53:31.5557  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 13:53:31.5557  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 14:09:06.9317  Health Monitoring started
2021-11-02 14:09:08.4247  Main application started. Version: R2021.1.6.573
2021-11-02 14:09:09.8687  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 14:09:09.9657  ParameterUI application started
2021-11-02 14:09:22.2898  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 14:09:22.2898  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 14:17:16.8974  ParameterUI application shut down
2021-11-02 14:17:16.9274  Health Monitoring shutdown
2021-11-02 14:17:16.9604  Main application shut down
2021-11-02 14:17:17.8936  Health Monitoring started
2021-11-02 14:17:19.3811  Main application started. Version: R2021.1.6.573
2021-11-02 14:17:20.4407  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 14:17:20.6677  ParameterUI application started
2021-11-02 14:17:31.8223  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 14:17:31.8223  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-02 15:17:32.2214  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-03 12:51:54.3643  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-04 07:51:08.2414  ParameterUI application shut down
2021-11-04 07:51:08.2865  Health Monitoring shutdown
2021-11-04 07:51:08.3285  Main application shut down
2021-11-04 07:51:09.2396  Health Monitoring started
2021-11-04 07:51:10.7400  Main application started. Version: R2021.1.6.573
2021-11-04 07:51:11.7220  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-04 07:51:12.1067  ParameterUI application started
2021-11-04 07:51:23.5414  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-04 07:51:23.5414  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-04 08:51:24.1506  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 11:25:02.9718  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 11:44:58.0726  ParameterUI application shut down
2021-11-09 11:44:58.1306  Health Monitoring shutdown
2021-11-09 11:44:58.1766  Main application shut down
2021-11-09 11:44:59.0406  Health Monitoring started
2021-11-09 11:45:00.5366  Main application started. Version: R2021.1.6.573
2021-11-09 11:45:01.6015  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 11:45:02.0135  ParameterUI application started
2021-11-09 11:45:13.4192  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 11:45:13.4192  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 12:45:14.1825  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 15:52:36.6081  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 16:36:09.2940  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-09 16:47:01.1291  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2021-11-10 11:04:48.0879  ParameterUI application shut down
2022-03-14 09:03:22.9653  Health Monitoring started
2022-03-14 09:03:24.4523  Main application started. Version: R2021.1.6.573
2022-03-14 09:03:25.8753  ParameterUI application started
2022-03-14 09:03:25.9393  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 09:03:37.7154  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 09:03:37.7154  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 09:49:38.7405  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 10:03:38.3994  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 14:19:17.3374  ParameterUI application shut down
2022-03-14 14:19:17.3214  Health Monitoring shutdown
2022-03-14 14:19:17.4434  Main application shut down
2022-03-14 14:21:29.5313  Health Monitoring started
2022-03-14 14:21:30.9785  Main application started. Version: R2021.1.6.573
2022-03-14 14:21:31.9928  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 14:21:32.3288  ParameterUI application started
2022-03-14 14:21:43.4262  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 14:21:43.4262  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 14:29:01.9715  ParameterUI application shut down
2022-03-14 14:29:02.0592  Main application shut down
2022-03-14 14:29:02.0812  Health Monitoring shutdown
2022-03-14 14:29:02.9633  Health Monitoring started
2022-03-14 14:29:04.4739  Main application started. Version: R2021.1.6.573
2022-03-14 14:29:05.5026  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 14:29:05.8436  ParameterUI application started
2022-03-14 14:29:17.7504  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 14:29:17.7504  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 15:29:18.0576  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:01:09.2763  ParameterUI application shut down
2022-03-14 16:01:09.3250  Main application shut down
2022-03-14 16:01:52.7588  Health Monitoring shutdown
2022-03-14 16:03:15.8696  Health Monitoring started
2022-03-14 16:03:17.4284  Main application started. Version: R2021.1.9.573
2022-03-14 16:03:18.5149  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:03:18.8864  ParameterUI application started
2022-03-14 16:03:29.9021  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:03:29.9021  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:36:53.8127  ParameterUI application shut down
2022-03-14 16:36:53.8267  Health Monitoring shutdown
2022-03-14 16:36:53.8844  Main application shut down
2022-03-14 16:37:48.3643  Health Monitoring started
2022-03-14 16:37:49.8664  Main application started. Version: R2021.1.9.573
2022-03-14 16:37:50.8243  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:37:51.1713  ParameterUI application started
2022-03-14 16:38:01.9490  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:38:01.9490  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:41:08.6442  ParameterUI application shut down
2022-03-14 16:41:08.6872  Health Monitoring shutdown
2022-03-14 16:41:08.7442  Main application shut down
2022-03-14 16:41:09.6376  Health Monitoring started
2022-03-14 16:41:11.1423  Main application started. Version: R2021.1.9.573
2022-03-14 16:41:12.1129  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:41:12.4377  ParameterUI application started
2022-03-14 16:41:24.1811  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:41:24.1811  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:53:20.8092  ParameterUI application shut down
2022-03-14 16:53:20.8995  Health Monitoring shutdown
2022-03-14 16:53:20.9391  Main application shut down
2022-03-14 16:53:21.8461  Health Monitoring started
2022-03-14 16:53:23.3352  Main application started. Version: R2021.1.9.573
2022-03-14 16:53:24.3621  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:53:24.7816  ParameterUI application started
2022-03-14 16:53:36.5722  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 16:53:36.5722  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-14 17:53:36.9319  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-16 15:11:16.7181  ParameterUI application shut down
2022-03-16 15:11:16.7381  Health Monitoring shutdown
2022-03-16 15:11:16.8578  Main application shut down
2022-03-16 15:11:17.7046  Health Monitoring started
2022-03-16 15:11:19.1720  Main application started. Version: R2021.1.9.573
2022-03-16 15:11:20.2226  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-16 15:11:20.5406  ParameterUI application started
2022-03-16 15:11:32.1782  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-16 15:11:32.1782  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-16 16:11:32.5098  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-18 18:25:28.1877  Health Monitoring started
2022-03-18 18:25:29.7157  Main application started. Version: R2021.1.9.573
2022-03-18 18:25:31.1907  ParameterUI application started
2022-03-18 18:25:31.2697  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-18 18:25:43.2834  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-18 18:25:43.2834  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-03-18 19:25:43.6179  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-05 16:25:55.3075  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-05 17:16:19.9191  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-05 17:16:20.0471  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:42:52.2376  ParameterUI application shut down
2022-04-07 12:42:52.3666  Main application shut down
2022-04-07 12:42:53.9856  Health Monitoring shutdown
2022-04-07 12:43:02.2256  Health Monitoring started
2022-04-07 12:43:03.7199  Main application started. Version: R2021.1.9.573
2022-04-07 12:43:04.8217  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:43:05.2157  ParameterUI application started
2022-04-07 12:43:15.9146  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:43:16.3019  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:43:16.3019  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:44:38.6362  ParameterUI application shut down
2022-04-07 12:44:38.6762  Health Monitoring shutdown
2022-04-07 12:44:38.7472  Main application shut down
2022-04-07 12:44:39.6475  Health Monitoring started
2022-04-07 12:44:41.1439  Main application started. Version: R2021.1.9.573
2022-04-07 12:44:42.2282  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:44:42.5272  ParameterUI application started
2022-04-07 12:44:54.4694  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:44:54.4694  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:44:56.3635  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:48:36.1686  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 12:48:36.2326  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-07 13:44:55.2566  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-09 14:02:02.6670  Health Monitoring started
2022-04-09 14:02:04.1820  Main application started. Version: R2021.1.9.573
2022-04-09 14:02:05.6460  ParameterUI application started
2022-04-09 14:02:05.7290  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-09 14:02:18.1935  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-09 14:02:18.1935  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-09 15:02:19.1586  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-13 11:57:13.0639  ParameterUI application shut down
2022-04-13 11:57:13.1139  Health Monitoring shutdown
2022-04-13 11:57:13.1989  Main application shut down
2022-04-13 11:57:14.0745  Health Monitoring started
2022-04-13 11:57:15.6302  Main application started. Version: R2021.1.9.573
2022-04-13 11:57:16.6583  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-13 11:57:17.1288  ParameterUI application started
2022-04-13 11:57:29.0345  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-13 11:57:29.0345  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-13 12:04:19.6134  Main application started. Version: R2021.1.9.573
2022-04-13 12:04:20.6454  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-13 12:04:31.9627  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-13 12:04:31.9627  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-13 13:04:32.1925  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-19 06:28:38.0125  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-19 07:45:58.1396  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-19 13:27:47.2287  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-04-27 10:32:28.1152  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-20 01:10:21.5794  Health Monitoring shutdown
2022-06-20 01:10:21.6354  ParameterUI application shut down
2022-06-20 01:10:21.8500  Main application shut down
2022-06-20 01:10:22.6140  Health Monitoring started
2022-06-20 01:10:24.1886  Main application started. Version: R2021.1.9.573
2022-06-20 01:10:25.3286  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-20 01:10:25.5683  ParameterUI application started
2022-06-20 01:10:37.9506  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-20 01:10:37.9506  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-20 01:11:23.6119  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-20 01:11:23.8209  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-20 01:11:23.9779  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-20 02:10:39.2501  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-21 05:38:50.0925  Health Monitoring started
2022-06-21 05:38:51.6634  Main application started. Version: R2021.1.9.573
2022-06-21 05:38:53.1474  ParameterUI application started
2022-06-21 05:38:53.5484  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-21 05:39:05.1651  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-21 05:39:05.1651  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-21 06:39:05.9910  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-21 09:29:16.7593  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-21 09:29:16.8714  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-21 09:29:16.9424  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-06-29 09:52:30.4038  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-07-01 13:13:55.4538  ParameterUI application started
2022-07-01 13:14:06.0562  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-07-01 13:14:48.3404  ParameterUI application started
2022-07-01 13:16:36.1266  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-07-01 13:16:36.3140  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-08-24 10:09:57.6906  ParameterUI application started
2022-08-25 09:21:54.4515  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-08-25 10:07:12.2904  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-09-03 14:14:56.4217  Health Monitoring shutdown
2022-09-03 14:14:56.4807  ParameterUI application shut down
2022-09-03 14:14:56.6807  Main application shut down
2022-09-03 14:14:57.4510  Health Monitoring started
2022-09-03 14:14:59.0054  Main application started. Version: R2021.1.9.573
2022-09-03 14:15:00.3715  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-09-03 14:15:00.6796  ParameterUI application started
2022-09-03 14:15:12.6357  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-09-03 14:15:12.6357  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-09-03 15:15:13.2814  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-09-04 06:03:49.5384  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-09-04 06:49:16.1825  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-09-04 06:49:16.4685  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-11-20 12:44:42.1636  Health Monitoring started
2022-11-20 12:44:43.7646  Main application started. Version: R2021.1.9.573
2022-11-20 12:44:45.2956  ParameterUI application started
2022-11-20 12:44:45.8046  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-11-20 12:44:58.6964  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-11-20 12:44:58.6964  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-11-20 13:44:59.6745  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-11-21 08:54:13.6386  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-11-21 09:03:06.3494  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-11-21 09:03:06.4294  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2022-12-01 07:11:52.1442  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-04 15:50:43.0212  Main application started. Version: R2021.1.9.573
2023-01-04 15:50:44.2282  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-04 15:50:55.7500  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-04 15:50:55.7500  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-04 16:50:55.9905  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-05 07:23:29.9550  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 08:48:53.8111  Health Monitoring started
2023-01-31 08:48:55.5241  Main application started. Version: R2021.1.9.573
2023-01-31 08:48:57.1541  ParameterUI application started
2023-01-31 08:48:58.0141  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 08:49:11.3849  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 08:49:11.3849  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 09:07:12.0156  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 09:07:12.2554  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 09:07:12.5259  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 09:49:13.6702  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 12:48:32.3115  Health Monitoring shutdown
2023-01-31 12:48:32.3145  ParameterUI application shut down
2023-01-31 12:48:32.4495  Main application shut down
2023-01-31 12:48:33.3653  Health Monitoring started
2023-01-31 12:48:34.8686  Main application started. Version: R2021.1.9.573
2023-01-31 12:48:35.9716  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 12:48:36.2944  ParameterUI application started
2023-01-31 12:48:48.2505  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 12:48:48.2505  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 13:48:49.9499  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-01-31 15:36:24.5212  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-02-03 10:43:50.4856  Health Monitoring started
2023-02-03 10:43:52.1536  Main application started. Version: R2021.1.9.573
2023-02-03 10:43:53.8566  ParameterUI application started
2023-02-03 10:43:55.0726  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-02-03 10:44:07.7927  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-02-03 10:44:07.7927  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-02-03 10:45:21.9312  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-02-03 11:44:09.8779  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-02-04 21:45:31.4260  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-02-04 21:45:31.9363  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-25 11:53:45.9743  Health Monitoring shutdown
2023-04-25 11:53:46.0763  ParameterUI application shut down
2023-04-25 11:53:46.4786  Main application shut down
2023-04-25 11:53:47.0759  Health Monitoring started
2023-04-25 11:53:49.4959  ParameterUI application started
2023-04-25 11:53:50.6558  Main application started. Version: R2021.1.9.573
2023-04-25 11:53:52.0910  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-25 11:54:05.3326  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-25 11:54:05.3326  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-25 11:54:55.0806  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-25 11:54:55.2676  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-25 11:54:55.3483  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-25 12:54:07.3006  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-04-30 00:02:54.2254  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 10:37:43.5204  Health Monitoring shutdown
2023-05-09 10:37:43.5974  ParameterUI application shut down
2023-05-09 10:37:43.7731  Main application shut down
2023-05-09 10:37:44.5898  Health Monitoring started
2023-05-09 10:37:46.1646  Main application started. Version: R2021.1.9.573
2023-05-09 10:37:47.4057  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 10:37:47.5749  ParameterUI application started
2023-05-09 10:37:59.4269  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 10:38:00.9086  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 10:38:00.9116  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 11:00:35.9347  ParameterUI application shut down
2023-05-09 11:00:35.9587  Health Monitoring shutdown
2023-05-09 11:00:36.0237  Main application shut down
2023-05-09 11:00:36.9837  Health Monitoring started
2023-05-09 11:00:38.5690  Main application started. Version: R2021.1.9.573
2023-05-09 11:00:39.8087  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 11:00:40.0790  ParameterUI application started
2023-05-09 11:00:52.2786  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 11:00:52.2786  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 11:34:19.3600  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 12:00:52.7027  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 13:08:01.8193  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-09 13:08:01.8633  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-13 01:05:04.7223  ParameterUI application started
2023-05-13 01:05:53.4379  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-05-13 01:05:53.8279  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-21 09:36:12.8344  Health Monitoring shutdown
2023-06-21 09:36:12.9054  ParameterUI application shut down
2023-06-21 09:36:13.2441  Main application shut down
2023-06-21 09:36:13.8390  Health Monitoring started
2023-06-21 09:36:15.4376  Main application started. Version: R2021.1.9.573
2023-06-21 09:36:16.6783  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-21 09:36:16.8603  ParameterUI application started
2023-06-21 09:36:29.6203  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-21 09:36:29.6203  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-21 10:36:30.2213  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-21 10:36:30.3972  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-21 12:49:22.9692  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-21 12:49:23.1642  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-26 09:56:13.1912  Health Monitoring shutdown
2023-06-26 09:56:13.2293  ParameterUI application shut down
2023-06-26 09:56:13.3329  Main application shut down
2023-06-26 09:56:14.2459  Health Monitoring started
2023-06-26 09:56:15.8277  Main application started. Version: R2021.1.9.573
2023-06-26 09:56:17.1678  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-26 09:56:17.2918  ParameterUI application started
2023-06-26 09:56:28.6449  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-26 09:56:31.5970  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-26 09:56:31.5990  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-26 10:56:32.7915  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-26 12:21:21.6010  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-26 12:21:21.7320  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-29 12:19:09.2982  ParameterUI application shut down
2023-06-29 12:28:01.8117  Health Monitoring started
2023-06-29 12:28:03.4697  Main application started. Version: R2021.1.9.573
2023-06-29 12:28:05.2157  ParameterUI application started
2023-06-29 12:28:06.3747  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-29 12:28:19.6001  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-29 12:28:19.6001  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-29 12:29:55.9457  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-29 12:29:56.1164  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-29 12:29:56.1844  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-06-29 13:28:21.0124  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-07-14 07:11:14.6752  ParameterUI application started
2023-07-14 07:11:25.1709  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-07-14 07:12:36.4613  ParameterUI application started
2023-07-14 07:14:02.5662  ParameterUI application started
2023-07-14 07:15:12.4334  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-07-14 07:15:12.5334  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 07:44:14.1240  Health Monitoring shutdown
2023-08-03 07:44:14.1460  ParameterUI application shut down
2023-08-03 07:44:14.4650  Main application shut down
2023-08-03 07:44:15.1862  Health Monitoring started
2023-08-03 07:44:16.6657  Main application started. Version: R2021.1.9.573
2023-08-03 07:44:17.7982  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 07:44:18.0332  ParameterUI application started
2023-08-03 07:44:28.9108  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 07:44:30.3268  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 07:44:30.3268  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 07:52:18.9414  Health Monitoring started
2023-08-03 07:52:20.5754  Main application started. Version: R2021.1.9.573
2023-08-03 07:52:22.2474  ParameterUI application started
2023-08-03 07:52:23.3984  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 07:52:36.9994  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 07:52:36.9994  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:16:21.9989  Health Monitoring started
2023-08-03 08:16:23.5589  Main application started. Version: R2021.1.9.573
2023-08-03 08:16:25.2279  ParameterUI application started
2023-08-03 08:16:26.3929  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:16:39.4208  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:16:39.4208  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:25:45.8291  Health Monitoring started
2023-08-03 08:25:47.3951  Main application started. Version: R2021.1.9.573
2023-08-03 08:25:49.0421  ParameterUI application started
2023-08-03 08:25:50.2011  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:26:03.3993  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:26:03.3993  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:35:23.9488  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:35:24.0978  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 08:35:24.1908  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-03 09:26:04.2693  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 00:05:30.9973  Main application started. Version: R2021.1.9.573
2023-08-21 00:05:32.2353  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 00:05:43.5041  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 00:05:43.5041  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 01:05:43.7593  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 07:00:50.5854  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 08:44:23.7873  ParameterUI application shut down
2023-08-21 08:44:23.7853  Health Monitoring shutdown
2023-08-21 08:44:23.8923  Main application shut down
2023-08-21 08:44:24.7782  Health Monitoring started
2023-08-21 08:44:26.2707  Main application started. Version: R2021.1.9.573
2023-08-21 08:44:27.4444  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 08:44:27.6373  ParameterUI application started
2023-08-21 08:44:39.5734  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2023-08-21 08:44:39.5734  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
