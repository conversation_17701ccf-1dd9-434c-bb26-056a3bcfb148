2020-11-05 09:42:39.0385|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Cameras found: 1 
2020-11-05 09:42:39.0385|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GevInterfaceSubnetIPAddress/GevInterfaceSubnetMask: ***********/*********** 
2020-11-05 09:42:39.0504|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f9 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500927 at ***************/*********** on ***********/*********** subnet match 
2020-11-05 09:42:39.0504|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f9 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500927 accessible over this nic 
2020-11-05 09:42:39.0504|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Baumer Optronic MXGC40 700005500927  ok. 
2020-11-05 09:42:39.0676|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 09:42:39.0676|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Discovery: 1 cameras found. 
2020-11-05 09:42:39.0676|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|FoundCamerasRequestResponder: 
2020-11-05 09:42:39.0676|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Baumer Optronic MXGC40 700005500927 GEV RW  ippAxsVertical False: camera used True True True  
2020-11-05 09:42:39.0676|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=opening cameras 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device DeviceID:             00_06_be_0f_de_f9 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Model:                MXGC40 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Vendor:               Baumer Optronic 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device TLType:               GEV 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device AccessStatus:         RW 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device UserID:               MXGC40 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceID (SN):               700005500927 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceManufacturerInfo:      F:0100375C/C:0100613C/BL3.5:01000013 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceFirmwareVersion:       CID:010007/PID:11094944 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCCP:                      ControlAccessSwitchoverActive 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentIPAddress:         *************** 
2020-11-05 09:42:39.4572|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentSubnetMask:        *********** 
2020-11-05 09:42:39.4707|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Node Count:                  16 
2020-11-05 09:42:39.4707|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 09:42:39.4707|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=initializing cameras 
2020-11-05 09:42:39.4707|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|
0 name                = 
0 WarpEnable          = True 1 0 0 0
0 SwapImageTopBotAtY  = 0
0 MirrorAxsHorizontal = False
0 MirrorAxsVertical   = True
0 RescaleEnable       = False
0 RescaleFactor       = 1
0 SubtractGreyvalue   = 0
 
2020-11-05 09:42:39.4707|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCDA:                     0.0.0.0 
2020-11-05 09:42:39.4707|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorWidth = 2040 [0..65535/1] 
2020-11-05 09:42:39.4707|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorHeight = 2044 [0..65535/1] 
2020-11-05 09:42:39.4881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningHorizontal = 1] 
2020-11-05 09:42:39.4881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 09:42:39.4881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningVertical = 1] 
2020-11-05 09:42:39.4881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 09:42:39.4881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [PixelFormat = Mono8] 
2020-11-05 09:42:39.4881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 PixelFormat = Mono8 [Mono12/Mono12Packed/Mono8] 
2020-11-05 09:42:39.4881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 09:42:39.5091|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 09:42:39.5091|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 09:42:39.5091|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..0/2] 
2020-11-05 09:42:39.5091|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Width = 2040] 
2020-11-05 09:42:39.5091|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Width = 2040 [4..2040/4] 
2020-11-05 09:42:39.5091|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Height = 2040] 
2020-11-05 09:42:39.5091|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Height = 2040 [2..2044/2] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [HqMode = Off] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 09:42:39.5179|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevHeartbeatTimeout = 10000] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevHeartbeatTimeout = 10000 [500..4294967295/1] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPSPacketSize = 9000] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPSPacketSize = 9000 [576..16110/2] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PacketDelay = 10000 bytes => for HXG/MXG/VLG..: 80000 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPD = 80000] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPD = 80000 [0..4294967295/1] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCFTD (FrameTransmissionDelay) 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCFTD = 0] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCFTD = 0 [0..4294967295/1] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevTimestampTickFrequency=1000000000 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TimeStampFrequency64: 1000000000 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime min..max = 20..1000000/0 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Shutter range: [20;1000000], current=4000 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 ExposureTime = 4000 [20..1000000/0] 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain min..max/ = 1..8/0 
2020-11-05 09:42:39.5551|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain range: [1;8], current=1 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Gain = 1 [1..8/0] 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceTemperatureExceeded not available. 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PayloadSize = 4161600 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Current:  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Reset  :  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineSelector = Line0] 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineSelector = Line0 [Line0/Line1/Line2/Line3] 
2020-11-05 09:42:39.5737|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineSelector=Line0 
2020-11-05 09:42:39.5835|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerHighTimeAbs = 5] 
2020-11-05 09:42:39.5835|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerHighTimeAbs = 5 [0..5000/0] 
2020-11-05 09:42:39.5835|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerHighTimeAbs=5 
2020-11-05 09:42:39.5835|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerLowTimeAbs = 5] 
2020-11-05 09:42:39.5835|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerLowTimeAbs = 5 [0..5000/0] 
2020-11-05 09:42:39.5835|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerLowTimeAbs=5 
2020-11-05 09:42:39.5835|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Datastreams:                 1 
2020-11-05 09:42:39.6228|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Open datastream:             Stream0 
2020-11-05 09:42:39.6228|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamAnnounceBufferMinimum: 2 
2020-11-05 09:42:39.6296|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 09:42:39.6296|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Filter 
2020-11-05 09:42:39.6296|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Socket 
2020-11-05 09:42:39.6296|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 09:42:39.6296|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Allocated buffers:           20 
2020-11-05 09:42:39.6296|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 0 1D052D63040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 1 1D053567040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 2 1D053D6E040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 3 1D05457F040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 4 1D054D89040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 5 1D055581040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 6 1D055D83040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 7 1D056586040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 8 1D056D89040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 9 1D05758F040 
2020-11-05 09:42:39.6425|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 10 1D057D98040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 11 1D058599040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 12 1D058D9F040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 13 1D0595AD040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 14 1D059DB3040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 15 1D05A5B0040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 16 1D05ADB9040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 17 1D05B5BD040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 18 1D05BDCD040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 19 1D05C5D8040 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Queued buffers:              20 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource 'All':         supported 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource:               All 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerMode:                 On 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime:                500 
2020-11-05 09:42:39.6612|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Register Event Mode to:      EVENT_HANDLER 
2020-11-05 09:42:39.6881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 AcquisitionStart 
2020-11-05 09:42:39.6881|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=ok. 
2020-11-05 09:42:39.6909|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetShutter=90 
2020-11-05 09:42:39.6909|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                90 
2020-11-05 09:42:39.6909|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetGain=1 
2020-11-05 09:42:39.6909|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:42:39.7093|Process 1132|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Enable triggers. 
2020-11-05 09:42:49.9751|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:42:49.9751|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                90 
2020-11-05 09:43:04.5652|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:43:04.5652|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                90 
2020-11-05 09:43:06.1151|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:43:06.1151|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 09:43:06.1151|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 09:43:07.3207|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:43:07.3207|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 09:43:07.3207|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 09:43:09.2743|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:43:09.2743|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 09:43:09.2892|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 09:43:09.2892|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:43:09.3005|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 09:43:09.3005|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 09:43:51.7962|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 09:43:51.7962|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 09:43:51.7962|Process 1132|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 14:15:15.2805|Process 1288|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|BaumerPictureSource2 created 
2020-11-05 14:15:15.2971|Process 1288|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|status=reading configuration 
2020-11-05 14:15:15.3183|Process 1288|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 StackPictures=False 
2020-11-05 14:15:15.3183|Process 1288|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Config: SN=700005500927, CamRect={X=0,Y=0,Width=2040,Height=2040}, PicSizeOut={Width=1020, Height=1020} 
2020-11-05 14:15:15.3183|Process 1288|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Camera0 warp => DefaultZoom=1 AngleDeg=0 OffsetX=0 OffsetY=0 
2020-11-05 14:15:15.3967|Process 1288|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartAcquisition|status=StartAcquisition 
2020-11-05 14:15:15.4278|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.CheckCameras|------------ 
2020-11-05 14:15:16.4713|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|StartCameraSubSystem 1 
2020-11-05 14:15:16.4917|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Disable triggers. 
2020-11-05 14:15:16.4917|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching systems 0 
2020-11-05 14:15:16.5202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected systems:  1 
2020-11-05 14:15:16.5202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:15:16.5202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:15:16.5202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:15:16.5202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:15:16.5202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System GEV found: C:\Symplex.NET\bgapi2_gige.cti_2.7.14708.14730 
2020-11-05 14:15:16.5202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|mSystem.Open() 
2020-11-05 14:15:16.5373|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:15:16.5373|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:15:16.5373|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:15:16.5373|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:15:16.5373|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GenTL Version:   1.5 
2020-11-05 14:15:16.5487|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=assigning interfaces 1 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected interfaces: 4 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {907ADD6B-2066-4BA6-BFB2-E4DAFD42CAAB} 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #2 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: 0.0.0.0/0.0.0.0 <- do not include 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4121C4C2-2085-4A6D-837F-FB555CAFED42} 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.5648|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection 
2020-11-05 14:15:16.5972|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ************/************* <- do not include 
2020-11-05 14:15:16.6355|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:15:16.6917|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4E4D9349-6426-4FD0-AC75-AC35B548AE0D} 
2020-11-05 14:15:16.7073|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.7073|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #3 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- do not include 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {8C712529-904F-4E1D-AC54-AFE171402265} 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) Ethernet Connection (7) I219-LM 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- include 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  use interface 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching cameras 1 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Checking interface:  
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {8C712529-904F-4E1D-AC54-AFE171402265} 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:15:16.7233|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) Ethernet Connection (7) I219-LM 
2020-11-05 14:15:17.6391|Process 1288|GrabberProcess|Thread 15|Base2-Grabber receive thread|ERROR|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Invalid gain range, cannot set gain. 
2020-11-05 14:15:21.7478|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Cameras found: 1 
2020-11-05 14:15:21.7478|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GevInterfaceSubnetIPAddress/GevInterfaceSubnetMask: ***********/*********** 
2020-11-05 14:15:21.7478|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f9 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500927 at ***************/*********** on ***********/*********** subnet match 
2020-11-05 14:15:21.7478|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f9 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500927 accessible over this nic 
2020-11-05 14:15:21.7478|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Baumer Optronic MXGC40 700005500927  ok. 
2020-11-05 14:15:21.7478|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:15:21.7478|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Discovery: 1 cameras found. 
2020-11-05 14:15:21.7665|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|FoundCamerasRequestResponder: 
2020-11-05 14:15:21.7665|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Baumer Optronic MXGC40 700005500927 GEV RW  ippAxsVertical False: camera used True True True  
2020-11-05 14:15:21.7665|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=opening cameras 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device DeviceID:             00_06_be_0f_de_f9 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Model:                MXGC40 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Vendor:               Baumer Optronic 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device TLType:               GEV 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device AccessStatus:         RW 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device UserID:               MXGC40 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceID (SN):               700005500927 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceManufacturerInfo:      F:0100375C/C:0100613C/BL3.5:01000013 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceFirmwareVersion:       CID:010007/PID:11094944 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCCP:                      ControlAccessSwitchoverActive 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentIPAddress:         *************** 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentSubnetMask:        *********** 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Node Count:                  16 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=initializing cameras 
2020-11-05 14:15:22.1036|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|
0 name                = 
0 WarpEnable          = True 1 0 0 0
0 SwapImageTopBotAtY  = 0
0 MirrorAxsHorizontal = False
0 MirrorAxsVertical   = True
0 RescaleEnable       = False
0 RescaleFactor       = 1
0 SubtractGreyvalue   = 0
 
2020-11-05 14:15:22.1223|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCDA:                     *********** 
2020-11-05 14:15:22.1223|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorWidth = 2040 [0..65535/1] 
2020-11-05 14:15:22.1223|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorHeight = 2044 [0..65535/1] 
2020-11-05 14:15:22.1223|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningHorizontal = 1] 
2020-11-05 14:15:22.1380|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:15:22.1380|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningVertical = 1] 
2020-11-05 14:15:22.1380|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:15:22.1380|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [PixelFormat = Mono8] 
2020-11-05 14:15:22.1380|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 PixelFormat = Mono8 [Mono12/Mono12Packed/Mono8] 
2020-11-05 14:15:22.1380|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Width = 2040] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Width = 2040 [4..2040/4] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Height = 2040] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Height = 2040 [2..2044/2] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [HqMode = Off] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:15:22.1548|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevHeartbeatTimeout = 10000] 
2020-11-05 14:15:22.2020|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevHeartbeatTimeout = 10000 [500..4294967295/1] 
2020-11-05 14:15:22.2020|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPSPacketSize = 9000] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPSPacketSize = 9000 [576..16110/2] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PacketDelay = 10000 bytes => for HXG/MXG/VLG..: 80000 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPD = 80000] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPD = 80000 [0..4294967295/1] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCFTD (FrameTransmissionDelay) 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCFTD = 0] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCFTD = 0 [0..4294967295/1] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevTimestampTickFrequency=1000000000 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TimeStampFrequency64: 1000000000 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime min..max = 20..1000000/0 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Shutter range: [20;1000000], current=20 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 ExposureTime = 20 [20..1000000/0] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain min..max/ = 1..8/0 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain range: [1;8], current=1 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Gain = 1 [1..8/0] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceTemperatureExceeded not available. 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PayloadSize = 4161600 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Current:  (HW) FrameCounter = 1535 cam.FrameCountSw = 0 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Reset  :  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineSelector = Line0] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineSelector = Line0 [Line0/Line1/Line2/Line3] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineSelector=Line0 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerHighTimeAbs = 5] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerHighTimeAbs = 5 [0..5000/0] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerHighTimeAbs=5 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerLowTimeAbs = 5] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerLowTimeAbs = 5 [0..5000/0] 
2020-11-05 14:15:22.2058|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerLowTimeAbs=5 
2020-11-05 14:15:22.2349|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Datastreams:                 1 
2020-11-05 14:15:22.2705|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Open datastream:             Stream0 
2020-11-05 14:15:22.2705|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamAnnounceBufferMinimum: 2 
2020-11-05 14:15:22.2705|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:15:22.2705|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Filter 
2020-11-05 14:15:22.2705|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Socket 
2020-11-05 14:15:22.2705|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Allocated buffers:           20 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 0 2217AE5A040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 1 2217B65F040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 2 2217BE66040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 3 2217C663040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 4 2217CE6D040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 5 2217D671040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 6 2217DE76040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 7 2217E67D040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 8 2217EE83040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 9 2217F68C040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 10 22100004040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 11 2210080A040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 12 22101007040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 13 22101800040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 14 2210200A040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 15 22102802040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 16 2210300E040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 17 2210381B040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 18 22104028040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 19 22104823040 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Queued buffers:              20 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource 'All':         supported 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource:               All 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerMode:                 On 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime:                500 
2020-11-05 14:15:22.2865|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Register Event Mode to:      EVENT_HANDLER 
2020-11-05 14:15:22.3202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 AcquisitionStart 
2020-11-05 14:15:22.3202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=ok. 
2020-11-05 14:15:22.3202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetShutter=10 
2020-11-05 14:15:22.3202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 14:15:22.3202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 14:15:22.3202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetGain=1 
2020-11-05 14:15:22.3202|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 14:15:22.3461|Process 1288|GrabberProcess|Thread 12||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Enable triggers. 
2020-11-05 14:19:31.9450|Process 2220|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|BaumerPictureSource2 created 
2020-11-05 14:19:31.9707|Process 2220|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|status=reading configuration 
2020-11-05 14:19:31.9901|Process 2220|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 StackPictures=False 
2020-11-05 14:19:31.9901|Process 2220|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Config: SN=700005500927, CamRect={X=0,Y=0,Width=2040,Height=2040}, PicSizeOut={Width=1020, Height=1020} 
2020-11-05 14:19:31.9901|Process 2220|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource..ctor|0 Camera0 warp => DefaultZoom=1 AngleDeg=0 OffsetX=0 OffsetY=0 
2020-11-05 14:19:32.0492|Process 2220|GrabberProcess|Thread 1||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartAcquisition|status=StartAcquisition 
2020-11-05 14:19:32.0804|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.CheckCameras|------------ 
2020-11-05 14:19:33.1163|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|StartCameraSubSystem 1 
2020-11-05 14:19:33.1345|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Disable triggers. 
2020-11-05 14:19:33.1345|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching systems 0 
2020-11-05 14:19:33.1814|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected systems:  1 
2020-11-05 14:19:33.1814|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:19:33.1814|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:19:33.1814|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:19:33.1814|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:19:33.1814|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System GEV found: C:\Symplex.NET\bgapi2_gige.cti_2.7.14708.14730 
2020-11-05 14:19:33.1950|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|mSystem.Open() 
2020-11-05 14:19:33.1950|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Name:     bgapi2_gige.cti 
2020-11-05 14:19:33.1950|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Type:     GEV 
2020-11-05 14:19:33.1950|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System Version:  2.7.14708.14730 
2020-11-05 14:19:33.1950|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|System PathName: C:\Symplex.NET\bgapi2_gige.cti 
2020-11-05 14:19:33.1950|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GenTL Version:   1.5 
2020-11-05 14:19:33.1950|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=assigning interfaces 1 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Detected interfaces: 4 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {907ADD6B-2066-4BA6-BFB2-E4DAFD42CAAB} 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #2 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: 0.0.0.0/0.0.0.0 <- do not include 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4121C4C2-2085-4A6D-837F-FB555CAFED42} 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection 
2020-11-05 14:19:33.2117|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ************/************* <- do not include 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {4E4D9349-6426-4FD0-AC75-AC35B548AE0D} 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) I210 Gigabit Network Connection #3 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- do not include 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  do not use interface 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {8C712529-904F-4E1D-AC54-AFE171402265} 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) Ethernet Connection (7) I219-LM 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevInterfaceSubnetIPAddress: ***********/*********** <- include 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|=>  use interface 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=searching cameras 1 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Checking interface:  
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface ID:   {8C712529-904F-4E1D-AC54-AFE171402265} 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Type: GEV 
2020-11-05 14:19:33.2282|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Interface Name: Intel(R) Ethernet Connection (7) I219-LM 
2020-11-05 14:19:34.5499|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|ERROR|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Invalid gain range, cannot set gain. 
2020-11-05 14:19:38.2684|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Cameras found: 1 
2020-11-05 14:19:38.2684|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|GevInterfaceSubnetIPAddress/GevInterfaceSubnetMask: ***********/*********** 
2020-11-05 14:19:38.2684|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f9 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500927 at ***************/*********** on ***********/*********** subnet match 
2020-11-05 14:19:38.2841|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Camera 00_06_be_0f_de_f9 Baumer Optronic MXGC40 GEV RW SerialNumber 700005500927 accessible over this nic 
2020-11-05 14:19:38.2841|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Baumer Optronic MXGC40 700005500927  ok. 
2020-11-05 14:19:38.2841|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:19:38.2841|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Discovery: 1 cameras found. 
2020-11-05 14:19:38.2841|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|FoundCamerasRequestResponder: 
2020-11-05 14:19:38.2841|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Baumer Optronic MXGC40 700005500927 GEV RW  ippAxsVertical False: camera used True True True  
2020-11-05 14:19:38.2841|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=opening cameras 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device DeviceID:             00_06_be_0f_de_f9 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Model:                MXGC40 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device Vendor:               Baumer Optronic 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device TLType:               GEV 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device AccessStatus:         RW 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Device UserID:               MXGC40 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceID (SN):               700005500927 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceManufacturerInfo:      F:0100375C/C:0100613C/BL3.5:01000013 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceFirmwareVersion:       CID:010007/PID:11094944 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCCP:                      ControlAccessSwitchoverActive 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentIPAddress:         *************** 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevCurrentSubnetMask:        *********** 
2020-11-05 14:19:38.6522|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Node Count:                  16 
2020-11-05 14:19:38.6731|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|------------ 
2020-11-05 14:19:38.6731|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=initializing cameras 
2020-11-05 14:19:38.6731|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|
0 name                = 
0 WarpEnable          = True 1 0 0 0
0 SwapImageTopBotAtY  = 0
0 MirrorAxsHorizontal = False
0 MirrorAxsVertical   = True
0 RescaleEnable       = False
0 RescaleFactor       = 1
0 SubtractGreyvalue   = 0
 
2020-11-05 14:19:38.6843|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCDA:                     0.0.0.0 
2020-11-05 14:19:38.6843|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorWidth = 2040 [0..65535/1] 
2020-11-05 14:19:38.6843|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 SensorHeight = 2044 [0..65535/1] 
2020-11-05 14:19:38.6911|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningHorizontal = 1] 
2020-11-05 14:19:38.6911|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:19:38.6911|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [BinningVertical = 1] 
2020-11-05 14:19:38.6911|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:19:38.6911|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [PixelFormat = Mono8] 
2020-11-05 14:19:38.6911|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 PixelFormat = Mono8 [Mono12/Mono12Packed/Mono8] 
2020-11-05 14:19:38.6911|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:19:38.7132|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:19:38.7132|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:19:38.7132|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..0/2] 
2020-11-05 14:19:38.7132|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Width = 2040] 
2020-11-05 14:19:38.7132|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Width = 2040 [4..2040/4] 
2020-11-05 14:19:38.7132|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [Height = 2040] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Height = 2040 [2..2044/2] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetX = 0] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetX = 0 [0..0/1] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [OffsetY = 0] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 OffsetY = 0 [0..4/2] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [HqMode = Off] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningHorizontal = 1 [1..2/1] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 BinningVertical = 1 [1..2/1] 
2020-11-05 14:19:38.7217|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevHeartbeatTimeout = 10000] 
2020-11-05 14:19:38.7635|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevHeartbeatTimeout = 10000 [500..4294967295/1] 
2020-11-05 14:19:38.7635|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPSPacketSize = 9000] 
2020-11-05 14:19:38.7635|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPSPacketSize = 9000 [576..16110/2] 
2020-11-05 14:19:38.7635|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PacketDelay = 10000 bytes => for HXG/MXG/VLG..: 80000 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCPD = 80000] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCPD = 80000 [0..4294967295/1] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevSCFTD (FrameTransmissionDelay) 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [GevSCFTD = 0] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 GevSCFTD = 0 [0..4294967295/1] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 GevTimestampTickFrequency=1000000000 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TimeStampFrequency64: 1000000000 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime min..max = 20..1000000/0 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Shutter range: [20;1000000], current=4000 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 ExposureTime = 4000 [20..1000000/0] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain min..max/ = 1..8/0 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Gain range: [1;8], current=1 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 Gain = 1 [1..8/0] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 DeviceTemperatureExceeded not available. 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 PayloadSize = 4161600 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Current:  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|GrabberExt|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Reset  :  (HW) FrameCounter = 0 cam.FrameCountSw = 0 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineSelector = Line0] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineSelector = Line0 [Line0/Line1/Line2/Line3] 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineSelector=Line0 
2020-11-05 14:19:38.7688|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerHighTimeAbs = 5] 
2020-11-05 14:19:38.7902|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerHighTimeAbs = 5 [0..5000/0] 
2020-11-05 14:19:38.7902|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerHighTimeAbs=5 
2020-11-05 14:19:38.7902|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetCamParam|0 [LineDebouncerLowTimeAbs = 5] 
2020-11-05 14:19:38.7902|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.LogCamParam|0 LineDebouncerLowTimeAbs = 5 [0..5000/0] 
2020-11-05 14:19:38.7902|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 LineDebouncerLowTimeAbs=5 
2020-11-05 14:19:38.7902|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Datastreams:                 1 
2020-11-05 14:19:38.8264|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Open datastream:             Stream0 
2020-11-05 14:19:38.8264|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamAnnounceBufferMinimum: 2 
2020-11-05 14:19:38.8264|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:19:38.8264|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Filter 
2020-11-05 14:19:38.8372|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Available StreamDriverModel: Socket 
2020-11-05 14:19:38.8372|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 StreamDriverModel:           Filter 
2020-11-05 14:19:38.8372|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Allocated buffers:           20 
2020-11-05 14:19:38.8372|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 0 103F40B6040 
2020-11-05 14:19:38.8372|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 1 103F48BA040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 2 103F50B5040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 3 103F58B7040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 4 103F60B9040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 5 103F68BB040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 6 103F70CE040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 7 103F78D3040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 8 103F80D0040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 9 103F88D8040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 10 103F90DC040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 11 103F98EE040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 12 103FA0F0040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 13 103FA8F2040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 14 103FB0F0040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 15 103FB8FD040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 16 103FC100040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 17 103FC909040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 18 103FD101040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 19 103FD90E040 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Queued buffers:              20 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource 'All':         supported 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerSource:               All 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 TriggerMode:                 On 
2020-11-05 14:19:38.8501|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 ExposureTime:                500 
2020-11-05 14:19:38.8637|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 Register Event Mode to:      EVENT_HANDLER 
2020-11-05 14:19:38.8806|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 AcquisitionStart 
2020-11-05 14:19:38.8806|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|status=ok. 
2020-11-05 14:19:38.8806|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetShutter=10 
2020-11-05 14:19:38.8806|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 14:19:38.8806|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 14:19:38.8806|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|0 SetGain=1 
2020-11-05 14:19:38.8806|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 14:19:38.9033|Process 2220|GrabberProcess|Thread 11||INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.StartCameraSubSystem|Enable triggers. 
2020-11-05 16:09:53.0554|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 16:09:53.0554|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 16:09:53.0684|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 16:18:40.5595|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 16:18:40.5690|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-05 16:18:40.5690|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-05 16:18:42.0842|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-05 16:18:42.0842|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                30 
2020-11-06 06:41:35.7191|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-06 06:41:35.7191|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-06 06:41:35.7191|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-06 06:41:35.7191|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-06 06:41:35.7191|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-06 06:41:35.7345|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-06 06:41:36.3088|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-06 06:41:36.3088|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-06 06:41:36.3088|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-06 06:41:37.4599|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-06 06:41:37.4599|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-06 06:41:37.4599|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
2020-11-06 06:41:37.4599|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetGain|0 Gain:                        1 
2020-11-06 06:41:37.4599|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 correct shutter: 10 -> 20 
2020-11-06 06:41:37.4599|Process 2220|GrabberProcess|Thread 14|Base2-Grabber receive thread|INFO|Grabber|Symplex.PictureSources.Baumer2.BaumerPictureSource.SetShutter|0 ExposureTime:                20 
