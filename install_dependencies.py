#!/usr/bin/env python3
"""
Installation script for Industrial Log Analyzer
Installs all required dependencies and sets up the environment
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ Failed to install {package}")
        return False

def main():
    """Main installation routine"""
    print("Industrial Log Analyzer - Dependency Installation")
    print("=" * 60)
    
    # Required packages
    packages = [
        "pandas",
        "matplotlib",
        "numpy",
        "tkinter"  # Usually comes with Python
    ]
    
    # Optional packages for enhanced functionality
    optional_packages = [
        "seaborn",  # Enhanced plotting
        "plotly",   # Interactive plots
        "openpyxl", # Excel export
        "xlsxwriter" # Excel formatting
    ]
    
    print("Installing required packages...")
    success_count = 0
    
    for package in packages:
        if package == "tkinter":
            # tkinter comes with Python, try to import it
            try:
                import tkinter
                print("✓ tkinter is available")
                success_count += 1
            except ImportError:
                print("✗ tkinter not available - please install Python with tkinter support")
        else:
            if install_package(package):
                success_count += 1
    
    print(f"\nRequired packages: {success_count}/{len(packages)} installed successfully")
    
    # Install optional packages
    print("\nInstalling optional packages for enhanced functionality...")
    optional_success = 0
    
    for package in optional_packages:
        if install_package(package):
            optional_success += 1
    
    print(f"Optional packages: {optional_success}/{len(optional_packages)} installed successfully")
    
    # Create desktop shortcut (Windows)
    if sys.platform == "win32":
        try:
            create_windows_shortcut()
        except Exception as e:
            print(f"Could not create desktop shortcut: {e}")
    
    print("\n" + "=" * 60)
    if success_count == len(packages):
        print("✓ Installation completed successfully!")
        print("You can now run the log analyzer with: python log_analyzer.py")
    else:
        print("⚠ Installation completed with some issues")
        print("Please check the error messages above and install missing packages manually")

def create_windows_shortcut():
    """Create a desktop shortcut on Windows"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Industrial Log Analyzer.lnk")
        target = os.path.join(os.getcwd(), "log_analyzer.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✓ Desktop shortcut created")
        
    except ImportError:
        # Try without winshell
        try:
            from win32com.client import Dispatch
            
            desktop = os.path.join(os.path.expanduser("~"), "Desktop")
            path = os.path.join(desktop, "Industrial Log Analyzer.lnk")
            target = os.path.join(os.getcwd(), "log_analyzer.py")
            wDir = os.getcwd()
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = wDir
            shortcut.save()
            
            print("✓ Desktop shortcut created")
            
        except Exception:
            print("⚠ Could not create desktop shortcut (pywin32 not available)")

if __name__ == "__main__":
    main()
