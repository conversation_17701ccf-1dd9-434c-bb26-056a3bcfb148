2024-11-04 06:16:42.2495  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 06:16:54.6168  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 06:16:54.6168  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 06:17:09.3640  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 07:16:55.1452  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 07:48:48.4149  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 11:31:50.7533  ParameterUI application shut down
2024-11-04 11:31:50.7363  Health Monitoring shutdown
2024-11-04 11:31:50.9133  Main application shut down
2024-11-04 11:31:51.7857  Health Monitoring started
2024-11-04 11:31:53.3127  Main application started. Version: R2021.1.9.573
2024-11-04 11:31:54.5435  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 11:31:54.8223  ParameterUI application started
2024-11-04 11:32:06.8434  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 11:32:06.8434  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 11:32:20.8273  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 12:32:08.5204  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 13:12:19.2785  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 13:12:19.4145  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-04 13:12:19.5035  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 00:15:07.0800  Main application started. Version: R2021.1.9.573
2024-11-07 00:15:08.3210  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 00:15:19.5815  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 00:15:19.5815  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 00:15:35.4398  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 01:15:19.8450  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 16:34:22.7285  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 23:43:57.2486  Main application started. Version: R2021.1.9.573
2024-11-07 23:43:58.4546  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 23:44:11.1694  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 23:44:11.1694  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-07 23:44:26.2079  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-08 00:44:11.3627  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-11 07:23:53.4703  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 15:17:27.7752  ParameterUI application shut down
2024-11-17 15:19:17.9266  Health Monitoring started
2024-11-17 15:19:19.5456  Main application started. Version: R2021.1.9.573
2024-11-17 15:19:21.1886  ParameterUI application started
2024-11-17 15:19:22.8266  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 15:19:36.2811  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 15:19:36.2811  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 15:19:38.6240  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 15:21:06.8653  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 15:21:07.0073  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 15:21:07.1363  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2024-11-17 16:19:38.9557  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:02:28.6101  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:04:12.4196  ParameterUI application shut down
2025-01-10 17:06:04.4396  Health Monitoring started
2025-01-10 17:06:05.9906  Main application started. Version: R2021.1.9.573
2025-01-10 17:06:07.5506  ParameterUI application started
2025-01-10 17:06:09.1246  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:06:22.1381  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:06:22.1381  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:06:23.5551  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:07:09.4889  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:07:09.7893  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 17:07:16.9879  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-10 18:06:23.6323  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-13 00:02:12.9742  Main application started. Version: R2021.1.9.573
2025-01-13 00:02:14.1962  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-13 00:02:25.3726  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-13 00:02:25.3726  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-13 00:02:41.6919  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-13 01:02:25.5728  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-14 07:51:18.0200  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-24 12:09:33.4191  Health Monitoring shutdown
2025-01-24 12:09:33.4651  ParameterUI application shut down
2025-01-24 12:09:33.9543  Main application shut down
2025-01-24 12:09:34.5186  Health Monitoring started
2025-01-24 12:09:36.5488  ParameterUI application started
2025-01-24 12:09:37.5608  Main application started. Version: R2021.1.9.573
2025-01-24 12:09:38.8151  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-24 12:09:51.4251  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-24 12:09:51.4251  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-24 12:10:10.1000  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-24 13:09:52.9006  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-26 14:13:21.1530  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-27 09:16:09.6588  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-27 09:16:09.8038  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 10:37:08.5700  ParameterUI application shut down
2025-01-31 10:37:08.6310  Health Monitoring shutdown
2025-01-31 10:37:08.9880  Main application shut down
2025-01-31 10:37:09.5983  Health Monitoring started
2025-01-31 10:37:11.7031  ParameterUI application started
2025-01-31 10:37:12.6291  Main application started. Version: R2021.1.9.573
2025-01-31 10:37:14.0799  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 10:37:26.8425  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 10:37:26.8425  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 10:37:38.7397  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 10:45:59.0064  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 11:37:28.5473  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 20:20:29.4282  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-01-31 20:20:29.5502  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-05 16:55:47.9407  Main application started. Version: R2021.1.9.573
2025-02-05 16:55:49.1597  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-05 16:56:00.4489  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-05 16:56:00.4489  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-05 16:56:16.4645  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-05 17:56:00.6748  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-06 07:08:10.3667  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 12:27:05.1564  Health Monitoring shutdown
2025-02-10 12:27:05.1944  ParameterUI application shut down
2025-02-10 12:27:05.4484  Main application shut down
2025-02-10 12:27:06.2167  Health Monitoring started
2025-02-10 12:27:07.8199  Main application started. Version: R2021.1.9.573
2025-02-10 12:27:09.0868  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 12:27:09.2488  ParameterUI application started
2025-02-10 12:27:22.3869  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 12:27:22.3869  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 12:27:35.3122  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 12:30:02.0835  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 12:30:21.0789  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 12:30:21.3630  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-02-10 13:27:23.6335  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 07:39:37.4873  Health Monitoring shutdown
2025-03-07 07:39:37.4923  ParameterUI application shut down
2025-03-07 07:39:38.0543  Main application shut down
2025-03-07 07:39:38.5663  Health Monitoring started
2025-03-07 07:39:40.1328  Main application started. Version: R2021.1.9.573
2025-03-07 07:39:41.3951  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 07:39:41.4928  ParameterUI application started
2025-03-07 07:39:54.1160  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 07:39:54.1160  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 07:40:07.5968  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 07:44:35.4803  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 07:44:35.6253  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 07:44:35.7513  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-07 08:39:55.7392  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-21 12:02:29.5884  ParameterUI application started
2025-03-21 14:23:03.8896  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-03-21 14:23:04.1203  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-24 10:20:28.9909  Health Monitoring shutdown
2025-04-24 10:20:29.0726  ParameterUI application shut down
2025-04-24 10:20:29.6479  Main application shut down
2025-04-24 10:20:30.1527  Health Monitoring started
2025-04-24 10:20:33.3488  ParameterUI application started
2025-04-24 10:20:34.5023  Main application started. Version: R2021.1.9.573
2025-04-24 10:20:35.8786  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-24 10:20:49.1459  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-24 10:20:49.1459  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-24 10:20:59.0541  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-24 11:20:51.2200  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-24 17:08:37.2215  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-25 07:50:13.5366  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-04-25 07:50:13.7256  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 07:32:06.6390  ParameterUI application shut down
2025-05-19 07:32:06.6200  Health Monitoring shutdown
2025-05-19 07:32:07.1500  Main application shut down
2025-05-19 07:32:08.0550  Health Monitoring started
2025-05-19 07:32:09.7234  Main application started. Version: R2021.1.9.573
2025-05-19 07:32:11.1424  ParameterUI application started
2025-05-19 07:32:11.2294  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 07:32:24.4477  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 07:32:24.4477  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 07:32:36.6847  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 08:09:18.7899  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 08:09:18.9479  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 08:09:19.0209  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-19 08:32:25.2604  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-05-22 21:34:10.2524  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-06 01:01:50.3087  Main application started. Version: R2021.1.9.573
2025-06-06 01:01:51.6237  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-06 01:02:03.1580  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-06 01:02:03.1730  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-06 01:02:18.3291  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-06 02:02:03.4354  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-09 08:01:33.4667  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-17 14:46:19.9298  Main application started. Version: R2021.1.9.573
2025-06-17 14:46:21.4438  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-17 14:46:35.8310  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-17 14:46:35.8310  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-17 14:46:47.4565  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-17 14:48:07.6376  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-17 15:46:36.3098  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-18 00:28:43.1380  ParameterUI application started
2025-06-18 00:32:47.7717  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-18 03:54:50.7839  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[System.Collections.Generic.Queue`1[[Symplex.BaseInterfaces.DefectPosition, BaseInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]][], System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-06-18 03:56:38.1783  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 09:09:53.3444  ParameterUI application shut down
2025-07-03 09:09:53.3624  Health Monitoring shutdown
2025-07-03 09:09:53.7374  Main application shut down
2025-07-03 09:09:54.4425  Health Monitoring started
2025-07-03 09:09:56.1425  Main application started. Version: R2021.1.9.573
2025-07-03 09:09:57.5914  ParameterUI application started
2025-07-03 09:09:57.7784  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 09:10:11.5671  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 09:10:11.5671  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 09:10:23.4540  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 09:17:42.8947  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 10:10:12.7382  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 10:39:17.7114  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-03 10:39:17.8207  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-16 14:31:59.4408  Main application started. Version: R2021.1.9.573
2025-07-16 14:32:02.4758  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-16 14:32:15.5411  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-16 14:32:15.5411  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-16 14:32:24.2524  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-16 15:32:15.9569  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-17 07:31:26.5488  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-27 18:50:53.8825  Main application started. Version: R2021.1.9.573
2025-07-27 18:50:56.7394  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-27 18:51:10.9964  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-27 18:51:10.9964  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-27 18:51:22.0197  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-27 19:51:11.2474  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 03:02:07.3236  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:15:28.6644  Health Monitoring shutdown
2025-07-28 05:15:28.7344  ParameterUI application shut down
2025-07-28 05:15:28.8274  Main application shut down
2025-07-28 05:15:29.7171  Health Monitoring started
2025-07-28 05:15:31.3961  Main application started. Version: R2021.1.9.573
2025-07-28 05:15:32.7141  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:15:32.8413  ParameterUI application started
2025-07-28 05:15:46.2515  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:15:46.2515  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:15:58.6711  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:21:56.3208  Health Monitoring started
2025-07-28 05:21:57.8368  Main application started. Version: R2021.1.9.573
2025-07-28 05:21:59.8278  ParameterUI application started
2025-07-28 05:22:02.1868  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:22:16.0036  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:22:16.0046  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:22:33.4706  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:23:50.1556  ParameterUI application shut down
2025-07-28 05:23:50.1936  Health Monitoring shutdown
2025-07-28 05:23:50.2786  Main application shut down
2025-07-28 05:23:51.1605  Health Monitoring started
2025-07-28 05:23:52.6644  Main application started. Version: R2021.1.9.573
2025-07-28 05:23:53.8646  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:23:54.0276  ParameterUI application started
2025-07-28 05:24:06.2978  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:24:06.2978  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:24:20.2783  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:27:35.2199  Health Monitoring started
2025-07-28 05:27:36.7469  Main application started. Version: R2021.1.9.573
2025-07-28 05:27:38.3159  ParameterUI application started
2025-07-28 05:27:39.7669  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:27:52.9136  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:27:52.9136  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:28:01.0322  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:29:08.9354  ParameterUI application shut down
2025-07-28 05:29:08.9824  Health Monitoring shutdown
2025-07-28 05:29:09.0444  Main application shut down
2025-07-28 05:29:09.9393  Health Monitoring started
2025-07-28 05:29:11.3958  Main application started. Version: R2021.1.9.573
2025-07-28 05:29:12.5884  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:29:12.7745  ParameterUI application started
2025-07-28 05:29:25.0215  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:29:25.0255  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:29:39.0951  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:33:00.9826  Health Monitoring started
2025-07-28 05:33:02.5365  Main application started. Version: R2021.1.9.573
2025-07-28 05:33:04.1955  ParameterUI application started
2025-07-28 05:33:05.5945  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:33:19.4621  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:33:19.4621  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:33:24.2326  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:40:48.7009  ParameterUI application shut down
2025-07-28 05:40:48.7819  Health Monitoring shutdown
2025-07-28 05:40:48.7819  Main application shut down
2025-07-28 05:40:49.7254  Health Monitoring started
2025-07-28 05:40:51.1838  Main application started. Version: R2021.1.9.573
2025-07-28 05:40:52.3796  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:40:52.5829  ParameterUI application started
2025-07-28 05:41:04.4775  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:41:04.4775  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 05:41:18.8704  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 06:41:05.6753  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 08:30:23.1028  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 09:38:55.6557  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-28 09:38:55.7527  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 07:08:51.3930  ParameterUI application shut down
2025-07-30 07:10:43.3835  Health Monitoring started
2025-07-30 07:10:44.9305  Main application started. Version: R2021.1.9.573
2025-07-30 07:10:46.7915  ParameterUI application started
2025-07-30 07:10:49.1275  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 07:11:02.9462  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 07:11:02.9462  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 07:11:13.0910  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 07:15:01.4322  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 07:15:01.6343  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 07:15:01.7473  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 08:11:05.0672  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 09:07:49.3688  ParameterUI application shut down
2025-07-30 09:07:49.4298  Health Monitoring shutdown
2025-07-30 09:07:49.5928  Main application shut down
2025-07-30 09:07:50.5321  Health Monitoring started
2025-07-30 09:07:52.6542  ParameterUI application started
2025-07-30 09:07:53.6131  Main application started. Version: R2021.1.9.573
2025-07-30 09:07:55.0734  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 09:08:07.7578  Unknown type serialized: System.Collections.Generic.Dictionary`2[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089],[Symplex.ImageProcessing.ContainerPicsetCollector.KeepContainerInMemory, ImageProcessing, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 09:08:07.7578  Unknown type serialized: System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 09:08:19.5779  Unknown type serialized: System.Collections.Generic.List`1[[Symplex.HeissConnection.Messages.PictureSetFilter, HeissConnection, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 09:17:43.2150  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 09:17:43.4480  Unknown type serialized: System.Byte[,], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-30 10:08:09.7398  Unknown type serialized: System.Collections.Generic.HashSet`1[[System.Nullable`1[[System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], System.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
2025-07-31 08:46:05.8754  Unknown type serialized: System.Collections.Generic.List`1[[System.Collections.Generic.KeyValuePair`2[[Symplex.ParameterDatabase.Messages.ParameterDataItemKey, ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null],[Symplex.ParameterDatabase.Messages.ParameterDataItem`1[[System.Byte[], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], ParameterDatabase, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]], mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089 while serializing 
