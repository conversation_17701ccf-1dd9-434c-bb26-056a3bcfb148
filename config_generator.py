#!/usr/bin/env python3
"""
Industry-Specific Configuration Generator
=========================================
Generates tailored configurations for different types of industrial systems.
"""

import json
import os
from datetime import datetime

class ConfigGenerator:
    def __init__(self):
        self.base_config = {
            "analysis_settings": {
                "correlation_time_window_minutes": 5,
                "max_entries_detailed_view": 1000,
                "health_score_error_penalty": 2,
                "health_score_warning_penalty": 0.5,
                "critical_sequence_context_entries": 5
            },
            "gui_settings": {
                "default_window_size": "1400x900",
                "default_date_range_days": 2,
                "chart_colors": {
                    "critical": "red",
                    "high": "orange",
                    "medium": "yellow",
                    "low": "green",
                    "other": "blue"
                }
            },
            "export_settings": {
                "include_raw_logs": False,
                "max_export_entries": 10000,
                "timestamp_format": "%Y-%m-%d %H:%M:%S"
            }
        }
    
    def generate_symplex_config(self):
        """Generate configuration for Symplex inspection systems"""
        config = self.base_config.copy()
        
        config["system_type"] = "Symplex Inspection System"
        config["error_patterns"] = {
            "camera_system": {
                "pattern": "RC|RR|FL|FR|camera|Camera|Kamera|ImageProcessing|SidewallDimension|GrabberProcess",
                "severity_keywords": ["waiting for", "timeout", "communication error", "dark image"],
                "description": "Camera and image processing problems including RC/RR cameras"
            },
            "ethercat": {
                "pattern": "EtherCAT|EtherCat|Ecat|CVZ\\.|CBS\\.|CSW\\.|connection lost|slave failed",
                "severity_keywords": ["connection lost", "slave failed", "timeout", "communication interrupted"],
                "description": "EtherCAT communication issues with various modules"
            },
            "motor_drives": {
                "pattern": "Motor|Servo|Antrieb|Achse|drive|enable|Enable|Amplifier|Motorsteuerung",
                "severity_keywords": ["voltage", "disabled", "fault", "error", "not available"],
                "description": "Motor and servo drive issues including voltage problems"
            },
            "safety_system": {
                "pattern": "Schutztür|Protection|Safety|Sicherheit|#106[0-9]|door|Tür|Sicherheitskreis",
                "severity_keywords": ["open", "circuit", "violation", "activated"],
                "description": "Safety system activations including door opening"
            },
            "sensor_contamination": {
                "pattern": "sensor|Sensor|verschmutzt|dirty|contamination|Lichtschranke|trigger|Auslösesensor",
                "severity_keywords": ["dirty", "verschmutzt", "contamination", "blocked", "defective"],
                "description": "Sensor contamination and malfunction including trigger sensors"
            },
            "flash_lighting": {
                "pattern": "Flash Unit|FlashUnit|230V|Beleuchtung|lighting|Licht|sync|synchronization",
                "severity_keywords": ["230V", "connection", "sync", "synchronization", "failed"],
                "description": "Lighting and flash unit problems affecting camera synchronization"
            },
            "network_comm": {
                "pattern": "192\\.168\\.99\\.|network|Network|connection|Connection|timeout|Timeout|NetMsg",
                "severity_keywords": ["timeout", "connection lost", "unreachable", "failed"],
                "description": "Network connectivity issues within inspection system"
            },
            "inspection_process": {
                "pattern": "Inspection|Insp|Dimension|Dim_Insp|RealtimeInspDataCommunication|BinaryMessageReader",
                "severity_keywords": ["failed", "timeout", "communication", "processing error"],
                "description": "Inspection process and data communication failures"
            }
        }
        
        config["critical_codes"] = {
            "#1060": {
                "description": "Motorspannung nicht verfügbar",
                "severity": "critical",
                "category": "motor_drives",
                "recommended_action": "Check motor power supply and safety circuits"
            },
            "#1063": {
                "description": "Schutztür offen",
                "severity": "critical",
                "category": "safety_system",
                "recommended_action": "Close safety door and reset safety circuit"
            },
            "#1067": {
                "description": "Sicherheitskreis",
                "severity": "critical",
                "category": "safety_system",
                "recommended_action": "Check all safety components and reset system"
            },
            "#1080": {
                "description": "Auslösesensor verschmutzt",
                "severity": "high",
                "category": "sensor_contamination",
                "recommended_action": "Clean trigger sensor and recalibrate"
            },
            "#120": {
                "description": "Lichtschranke verschmutzt",
                "severity": "medium",
                "category": "sensor_contamination",
                "recommended_action": "Clean light barrier sensor"
            }
        }
        
        config["component_mapping"] = {
            "CVZ.5301-T1.1": "Motor Controller Axis 1",
            "CVZ.5301-T2.1": "Motor Controller Axis 2", 
            "CBS.2301-K1.1": "Communication Hub Base Station",
            "CSW.Switch": "EtherCAT Switch",
            "Flash Unit 230V": "Camera Flash Lighting System",
            "RC Camera": "Right Center Inspection Camera",
            "RR Camera": "Right Rear Inspection Camera",
            "************3:4900": "Image Processing Server",
            "************:3030": "Result Communication Server"
        }
        
        return config
    
    def generate_twincat_config(self):
        """Generate configuration for general TwinCAT systems"""
        config = self.base_config.copy()
        
        config["system_type"] = "TwinCAT Automation System"
        config["error_patterns"] = {
            "ethercat": {
                "pattern": "EtherCAT|EtherCat|Ecat|slave|Slave|Box|terminal",
                "severity_keywords": ["connection lost", "slave failed", "timeout", "not found"],
                "description": "EtherCAT communication and slave device issues"
            },
            "plc_runtime": {
                "pattern": "PLC|Runtime|TcRteX64|System Manager|TwinCAT",
                "severity_keywords": ["stopped", "exception", "error", "fault"],
                "description": "PLC runtime and TwinCAT system issues"
            },
            "motion_control": {
                "pattern": "NC|Motion|Axis|Drive|Servo|Motor",
                "severity_keywords": ["fault", "error", "following error", "limit"],
                "description": "Motion control and drive system problems"
            },
            "io_system": {
                "pattern": "Digital|Analog|Input|Output|IO|Terminal",
                "severity_keywords": ["fault", "error", "short circuit", "overload"],
                "description": "I/O system and terminal problems"
            },
            "safety": {
                "pattern": "Safety|Safe|Emergency|E-Stop|STO|SBC",
                "severity_keywords": ["triggered", "active", "fault", "violation"],
                "description": "Safety system activations and faults"
            }
        }
        
        config["critical_codes"] = {
            "0x4808": {
                "description": "EtherCAT slave not found",
                "severity": "critical",
                "category": "ethercat"
            },
            "0x4809": {
                "description": "EtherCAT communication error",
                "severity": "high", 
                "category": "ethercat"
            },
            "0x4A20": {
                "description": "Drive following error",
                "severity": "high",
                "category": "motion_control"
            }
        }
        
        return config
    
    def generate_packaging_config(self):
        """Generate configuration for packaging machinery"""
        config = self.base_config.copy()
        
        config["system_type"] = "Packaging Machinery"
        config["error_patterns"] = {
            "conveyor": {
                "pattern": "Conveyor|Transport|Belt|Chain|Speed",
                "severity_keywords": ["jam", "stopped", "speed error", "blocked"],
                "description": "Conveyor and transport system issues"
            },
            "filling": {
                "pattern": "Fill|Dose|Volume|Weight|Level",
                "severity_keywords": ["underfill", "overfill", "blocked", "sensor"],
                "description": "Filling and dosing system problems"
            },
            "sealing": {
                "pattern": "Seal|Heat|Temperature|Pressure",
                "severity_keywords": ["temperature", "pressure", "failed", "weak"],
                "description": "Sealing and heat treatment issues"
            },
            "labeling": {
                "pattern": "Label|Print|Barcode|Scanner",
                "severity_keywords": ["missing", "misaligned", "not readable", "jam"],
                "description": "Labeling and printing system problems"
            },
            "quality_control": {
                "pattern": "Quality|Check|Reject|Vision|Weight",
                "severity_keywords": ["out of spec", "rejected", "failed", "tolerance"],
                "description": "Quality control and inspection failures"
            }
        }
        
        return config
    
    def generate_robotics_config(self):
        """Generate configuration for robotic systems"""
        config = self.base_config.copy()
        
        config["system_type"] = "Robotic System"
        config["error_patterns"] = {
            "robot_control": {
                "pattern": "Robot|Axis|Joint|TCP|Cartesian",
                "severity_keywords": ["collision", "limit", "singularity", "unreachable"],
                "description": "Robot control and movement issues"
            },
            "gripper": {
                "pattern": "Gripper|Grip|Vacuum|Pressure|Clamp",
                "severity_keywords": ["lost", "failed", "pressure", "not closed"],
                "description": "Gripper and end-effector problems"
            },
            "vision_system": {
                "pattern": "Vision|Camera|Image|Recognition|Pattern",
                "severity_keywords": ["not found", "failed", "timeout", "quality"],
                "description": "Vision system and pattern recognition issues"
            },
            "safety_zones": {
                "pattern": "Safety|Zone|Fence|Scanner|Area",
                "severity_keywords": ["violated", "intrusion", "blocked", "fault"],
                "description": "Safety zone violations and scanner issues"
            }
        }
        
        return config
    
    def save_config(self, config, filename):
        """Save configuration to JSON file"""
        config["generated_on"] = datetime.now().isoformat()
        config["generator_version"] = "1.0"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"Configuration saved to {filename}")
    
    def generate_all_configs(self):
        """Generate all predefined configurations"""
        configs = {
            "symplex_config.json": self.generate_symplex_config(),
            "twincat_config.json": self.generate_twincat_config(),
            "packaging_config.json": self.generate_packaging_config(),
            "robotics_config.json": self.generate_robotics_config()
        }
        
        for filename, config in configs.items():
            self.save_config(config, filename)
        
        print(f"\nGenerated {len(configs)} configuration files:")
        for filename in configs.keys():
            print(f"  - {filename}")

def main():
    """Main function to generate configurations"""
    print("Industrial Log Analyzer - Configuration Generator")
    print("=" * 60)
    
    generator = ConfigGenerator()
    
    print("\nAvailable configuration types:")
    print("1. Symplex Inspection System")
    print("2. TwinCAT Automation System")
    print("3. Packaging Machinery")
    print("4. Robotic System")
    print("5. Generate all configurations")
    
    while True:
        try:
            choice = input("\nSelect configuration type (1-5): ").strip()
            
            if choice == "1":
                config = generator.generate_symplex_config()
                generator.save_config(config, "symplex_config.json")
                break
            elif choice == "2":
                config = generator.generate_twincat_config()
                generator.save_config(config, "twincat_config.json")
                break
            elif choice == "3":
                config = generator.generate_packaging_config()
                generator.save_config(config, "packaging_config.json")
                break
            elif choice == "4":
                config = generator.generate_robotics_config()
                generator.save_config(config, "robotics_config.json")
                break
            elif choice == "5":
                generator.generate_all_configs()
                break
            else:
                print("Invalid choice. Please select 1-5.")
        except KeyboardInterrupt:
            print("\nOperation cancelled.")
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("\nConfiguration generation complete!")
    print("\nTo use a specific configuration:")
    print("1. Rename the desired config file to 'config.json'")
    print("2. Restart the log analyzer application")

if __name__ == "__main__":
    main()
