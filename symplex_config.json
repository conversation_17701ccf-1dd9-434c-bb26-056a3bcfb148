{"analysis_settings": {"correlation_time_window_minutes": 5, "max_entries_detailed_view": 1000, "health_score_error_penalty": 2, "health_score_warning_penalty": 0.5, "critical_sequence_context_entries": 5}, "gui_settings": {"default_window_size": "1400x900", "default_date_range_days": 2, "chart_colors": {"critical": "red", "high": "orange", "medium": "yellow", "low": "green", "other": "blue"}}, "export_settings": {"include_raw_logs": false, "max_export_entries": 10000, "timestamp_format": "%Y-%m-%d %H:%M:%S"}, "system_type": "Symplex Inspection System", "error_patterns": {"camera_system": {"pattern": "RC|RR|FL|FR|camera|Camera|Kamera|ImageProcessing|SidewallDimension|GrabberProcess", "severity_keywords": ["waiting for", "timeout", "communication error", "dark image"], "description": "Camera and image processing problems including RC/RR cameras"}, "ethercat": {"pattern": "EtherCAT|EtherCat|Ecat|CVZ\\.|CBS\\.|CSW\\.|connection lost|slave failed", "severity_keywords": ["connection lost", "slave failed", "timeout", "communication interrupted"], "description": "EtherCAT communication issues with various modules"}, "motor_drives": {"pattern": "Motor|Servo|Antrieb|Achse|drive|enable|Enable|Amplifier|Motorsteuerung", "severity_keywords": ["voltage", "disabled", "fault", "error", "not available"], "description": "Motor and servo drive issues including voltage problems"}, "safety_system": {"pattern": "Schutztür|Protection|Safety|Sicherheit|#106[0-9]|door|Tür|Sicherheitskreis", "severity_keywords": ["open", "circuit", "violation", "activated"], "description": "Safety system activations including door opening"}, "sensor_contamination": {"pattern": "sensor|Sensor|verschmutzt|dirty|contamination|Lichtschranke|trigger|Auslösesensor", "severity_keywords": ["dirty", "verschmutzt", "contamination", "blocked", "defective"], "description": "Sensor contamination and malfunction including trigger sensors"}, "flash_lighting": {"pattern": "Flash Unit|FlashUnit|230V|Beleuchtung|lighting|Licht|sync|synchronization", "severity_keywords": ["230V", "connection", "sync", "synchronization", "failed"], "description": "Lighting and flash unit problems affecting camera synchronization"}, "network_comm": {"pattern": "192\\.168\\.99\\.|network|Network|connection|Connection|timeout|Timeout|NetMsg", "severity_keywords": ["timeout", "connection lost", "unreachable", "failed"], "description": "Network connectivity issues within inspection system"}, "inspection_process": {"pattern": "Inspection|Insp|Dimension|Dim_Insp|RealtimeInspDataCommunication|BinaryMessageReader", "severity_keywords": ["failed", "timeout", "communication", "processing error"], "description": "Inspection process and data communication failures"}}, "critical_codes": {"#1060": {"description": "Motorspannung nicht verfügbar", "severity": "critical", "category": "motor_drives", "recommended_action": "Check motor power supply and safety circuits"}, "#1063": {"description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> offen", "severity": "critical", "category": "safety_system", "recommended_action": "Close safety door and reset safety circuit"}, "#1067": {"description": "Sicherheitskreis", "severity": "critical", "category": "safety_system", "recommended_action": "Check all safety components and reset system"}, "#1080": {"description": "Auslösesensor verschmutzt", "severity": "high", "category": "sensor_contamination", "recommended_action": "Clean trigger sensor and recalibrate"}, "#120": {"description": "Lichtschranke verschmutzt", "severity": "medium", "category": "sensor_contamination", "recommended_action": "Clean light barrier sensor"}}, "component_mapping": {"CVZ.5301-T1.1": "Motor Controller Axis 1", "CVZ.5301-T2.1": "Motor Controller Axis 2", "CBS.2301-K1.1": "Communication Hub Base Station", "CSW.Switch": "EtherCAT Switch", "Flash Unit 230V": "Camera Flash Lighting System", "RC Camera": "Right Center Inspection Camera", "RR Camera": "Right Rear Inspection Camera", "************3:4900": "Image Processing Server", "************:3030": "Result Communication Server"}, "generated_on": "2025-07-31T14:55:50.374419", "generator_version": "1.0"}